import type { PageList, Pageable } from '../common/type'
import type { Dept, DeptCreate, DeptSearch, DeptUpdate } from './types'
import { kyDelete, kyGet, kyPost } from '~/utils/request'

export const deptApi = {
  create: (param: DeptCreate) => kyPost('dept/create', param),
  delete: (id: string) => kyDelete(`dept/${id}`),
  move: (id: string, parentId: string) => kyPost(`dept/${id}/${parentId}`),
  update: (id: string, param: DeptUpdate) => kyPost(`dept/update/${id}`, param),
  page: (param: Pageable<DeptSearch>) => kyPost(`dept/page`, param).json<PageList<Dept>>(),
  listChildren: (id: string) => kyGet(`dept/listChildren/${id}`).json<Dept[]>(),
  get: (id: string) => kyGet(`dept/${id}`).json<Dept>(),
  listDescendants: (code: string) => kyGet(`dept/listDescendants/${code}`).json<Dept[]>(),
}
