const designWidth: number = 1536 // 设计稿基准宽度 (1920 / 1.25)
const baseFontSize: number = 14 // 设计稿基准 rem (1rem = 14px)

function setRem(): void {
  const clientWidth = document.documentElement.clientWidth
  const scale = clientWidth / designWidth
  const currentFontSize = baseFontSize * scale
  document.documentElement.style.fontSize = `${currentFontSize}px`
}

export function responsivePx(px: number): number {
  const clientWidth = document.documentElement.clientWidth || window.innerWidth
  const scale = clientWidth / designWidth
  return px * scale
}

const responsive = {
  install(): void {
    setRem()
    window.addEventListener('resize', setRem)
  },
}

export default responsive
