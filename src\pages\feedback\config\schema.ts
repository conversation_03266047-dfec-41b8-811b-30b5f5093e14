import { toTypedSchema } from '@vee-validate/zod'

const feedbackConfigSearchSchema = toTypedSchema(
  z.object({
    anomaliesCode: z.string().optional(),
    anomaliesName: z.string().optional(),
  }),
)

const feedbackConfigCreateSchema = toTypedSchema(
  z.object({
    lineCode: z.string({
      required_error: '请选择所属线体',
    }).min(1),
    anomaliesCode: z.string({
      required_error: '请选择异常大类',
    }).min(1),
    anomaliesName: z.string().min(1).optional(),
    enable: z.boolean().default(false),
    responseConfig: z.array(z.object({
      pointTime: z.number(),
      responders: z.array(z.object({
        respondentId: z.string().min(1),
        respondentName: z.string().min(1).optional(),
      })).min(1, '至少选择一个响应人').max(1000, '人数超过限制').default([]),
      reporters: z.array(z.object({
        reporterId: z.string().min(1),
        reportName: z.string().min(1).optional(),
      })).min(1, '至少选择一个告知人').max(1000, '人数超过限制').default([]),
    })).min(1, '至少添加一条配置').default([]),
  }),
)
const feedbackConfigUpdateSchema = toTypedSchema(
  z.object({
    lineCode: z.string({
      required_error: '请选择所属线体',
    }).min(1),
    anomaliesCode: z.string({
      required_error: '请选择异常大类',
    }).min(1),
    anomaliesName: z.string().min(1).optional(),
    enable: z.boolean().default(false),
    responseConfig: z.array(z.object({
      pointTime: z.number(),
      responders: z.array(z.object({
        respondentId: z.string().min(1),
        respondentName: z.string().min(1).optional(),
      })).min(1, '至少选择一个响应人').max(1000, '人数超过限制').default([]),
      reporters: z.array(z.object({
        reporterId: z.string().min(1),
        reportName: z.string().min(1).optional(),
      })).min(1, '至少选择一个告知人').max(1000, '人数超过限制').default([]),
    })).min(1, '至少添加一条配置').default([]),
  }),
)

export function useFeedbackConfigSearchForm() {
  const form = useForm({
    validationSchema: feedbackConfigSearchSchema,
  })
  return form
}

export function useFeedBackConfigCreateForm() {
  const form = useForm({
    validationSchema: feedbackConfigCreateSchema,
  })
  return form
}

export function useFeedBackConfigUpdateForm() {
  const form = useForm({
    validationSchema: feedbackConfigUpdateSchema,
  })
  return form
}
