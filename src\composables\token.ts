import type { Jwt } from '~/utils/jwt'
import { parseJwt } from '~/utils/jwt'

export function useToken() {
  const accessToken = useLocalStorage<string | undefined>(import.meta.env.VITE_APP_ACCESS_STORE, undefined)
  const refreshToken = useLocalStorage<string | undefined>(import.meta.env.VITE_APP_REFRESH_STORE, undefined)
  const needRefresh = () => {
    if (!accessToken.value) {
      return false
    }
    const jwt: Jwt = parseJwt(accessToken.value)
    return jwt.exp - new Date().getTime() / 1000 <= 12 * 3600
  }

  const hasAccessToken = computed(() => {
    return !!accessToken.value
  })

  const isExpire = () => {
    if (!accessToken.value) {
      return false
    }
    const jwt: Jwt = parseJwt(accessToken.value)
    return jwt.exp - new Date().getTime() / 1000 <= 60
  }

  const needLogin = () => {
    if (!refreshToken.value) {
      return true
    }
    const jwt: Jwt = parseJwt(refreshToken.value)
    return jwt.exp - new Date().getTime() / 1000 <= 60
  }

  return { needLogin, needRefresh, isExpire, hasAccessToken, accessToken, refreshToken }
}
