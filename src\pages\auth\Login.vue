<script setup lang="ts">
import { Float<PERSON><PERSON><PERSON>, Password } from 'primevue'
import { useRoute, useRouter } from 'vue-router'
import { z } from 'zod'
import { HTTPError } from 'ky'
import { toTypedSchema } from '@vee-validate/zod'

import { authApi } from '~/api/common/auth'
import { useUserStore } from '~/stores/user'
import logo from '~/assets/logo.svg'

const route = useRoute()
const router = useRouter()

const schema = toTypedSchema(
  z.object({
    username: z.string().min(1),
    password: z.string().min(1),
  }),
)

const form = useForm({
  validationSchema: schema,
})
const username = useField<string | undefined>('username')
const password = useField<string | undefined>('password')
const params = route.query
const userStore = useUserStore()
if (params.code && params.state) {
  authApi.loginByCasdoor({ code: params.code.toString(), state: params.state.toString() }).then((res) => {
    userStore.clear()
    userStore.login(res.accessToken, res.refreshToken, res.user)
    if (params.redirect && params.redirect !== '/login')
      router.push(params.redirect.toString())
    else
      router.push('/')
  })
}

const onLogin = form.handleSubmit(async (values) => {
  try {
    const res = await authApi.login(values)
    userStore.login(res.accessToken, res.refreshToken, res.user)
    if (params.redirect) {
      router.push(params.redirect.toString())
    }
    else {
      router.push('/theoretical')
    }
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      errorToast({
        summary: '登录失败',
        detail: ex.message,
      })
    }
  }
})
</script>

<template>
  <div class="h-screen w-screen flex items-center justify-center">
    <Panel
      pt:header:class="hidden" pt:content:class=" flex flex-col items-center justify-center gap-8 bg-contrast px-24 py-12"
    >
      <div class="mb-4 flex gap-4">
        <Image :src="logo" width="56" height="56" />
        <div class="text-bold flex items-center text-8 font-bold">
          宏景电子
        </div>
      </div>

      <form @submit="onLogin">
        <div class="flex flex-col gap-8">
          <FloatLabel>
            <InputText v-model="username.value.value" input-id="username" size="large" />
            <label for="username">用户名</label>
          </FloatLabel>
          <FloatLabel>
            <Password v-model="password.value.value" input-id="password" size="large" :feedback="false" />
            <label for="password">密码</label>
          </FloatLabel>
          <Button type="submit" fluid size="large" label="登录" />
        </div>
      </form>
    </Panel>
  </div>
</template>
