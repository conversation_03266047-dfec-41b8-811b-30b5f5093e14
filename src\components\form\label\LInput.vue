<script setup lang="ts">
import type { InputTextProps } from 'primevue/inputtext'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
  inputProps?: InputTextProps
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <LabelFormItem :label="label" :name="name">
    <InputText :id="props.name" v-model="value" :invalid="errorMessage ? true : false" fluid v-bind="inputProps" />
    <ErrorMsg :error-message="errorMessage" />
  </LabelFormItem>
</template>
