import type { PageList, Pageable } from '../common/type'
import type { UnmetReason, UnmetReasonCreateParam } from './type'
import { kyBatchDelete, kyDelete, kyGet, kyPost, kyPostFile } from '~/utils/request'

export const unmetReasonApi = {
  page: (param: Pageable<Partial<UnmetReason>>) => kyPost('unmet-reasons/page', param).json<PageList<UnmetReason>>(),
  create: (param: UnmetReasonCreateParam) => kyPost('unmet-reasons', param),
  update: (id: string, param: UnmetReason) => kyPost(`unmet-reasons/${id}`, param),
  delete: (id: string) => kyDelete(`unmet-reasons/${id}`),
  batchDelete: (ids: string[]) => kyBatchDelete('unmet-reasons/batch', { json: ids }),
  get: (id: string) => kyGet(`unmet-reasons/${id}`).json<UnmetReason>(),
  import: (formData: FormData) => kyPostFile('unmet-reasons/import', formData),
  downloadTemplate: () => kyGet('unmet-reasons/template'),
  export: (param?: Partial<UnmetReason>) => kyPost('unmet-reasons/export', param),
}
