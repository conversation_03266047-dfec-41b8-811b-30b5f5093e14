<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { useForm } from 'vee-validate'
import { SolutionApi } from '~/api/feedback/solution'
import type { FeedbackTriggerSolutionCreateParam } from '~/api/feedback/solution/type'
import { TriggerSendApi } from '~/api/feedback/trigger-send'
import type { FeedbackTriggerSendWithId } from '~/api/feedback/trigger-send/type'
import { error, success } from '~/composables/toast'
import { useUserStore } from '~/stores/user'

const props = defineProps<{
  triggerRecordId?: string
}>()

const emits = defineEmits<{ save: [] }>()
const loading = ref(false)
const loadingSendRecords = ref(false)
const open = defineModel<boolean>('open')
const userStore = useUserStore()

// 触发发送记录列表
const triggerSendRecords = ref<FeedbackTriggerSendWithId[]>([])
const selectedSendRecordId = ref<string>()

// 表单验证模式
const SolutionCreateSchema = toTypedSchema(
  z.object({
    solution: z.string().min(1, '请输入解决方案'),
    solveTime: z.date({
      required_error: '请选择解决时间',
    }),
    triggerSendId: z.string().min(1, '请选择触发发送记录'),
  }),
)

const { handleSubmit, resetForm, setFieldValue } = useForm({
  validationSchema: SolutionCreateSchema,
  initialValues: {
    solution: '',
    solveTime: new Date(),
    triggerSendId: '',
  },
})

// 获取触发发送记录列表
async function fetchTriggerSendRecords() {
  if (!props.triggerRecordId)
    return

  try {
    loadingSendRecords.value = true
    const records = await TriggerSendApi.findByTriggerRecordId(props.triggerRecordId)
    triggerSendRecords.value = records

    // 如果只有一条记录，自动选择
    if (records.length === 1) {
      selectedSendRecordId.value = records[0].id
      setFieldValue('triggerSendId', records[0].id)
    }
  }
  catch (err) {
    console.error('获取触发发送记录失败:', err)
    error('获取触发发送记录失败')
  }
  finally {
    loadingSendRecords.value = false
  }
}

// 保存解决方案
const save = handleSubmit(async (values) => {
  if (!props.triggerRecordId) {
    error('缺少触发记录ID')
    return
  }

  try {
    loading.value = true
    const solutionParam: FeedbackTriggerSolutionCreateParam = {
      solution: values.solution.trim(),
      solveTime: values.solveTime,
      triggerRecordId: props.triggerRecordId,
      triggerSendId: values.triggerSendId,
      solverId: userStore.user?.id || '',
      solverName: userStore.user?.name || '',
    }

    await SolutionApi.create(solutionParam)
    success('解决方案创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  catch (err) {
    console.error('创建解决方案失败:', err)
    error('创建解决方案失败')
  }
  finally {
    loading.value = false
  }
})

// 对话框显示时初始化
function onShow() {
  resetForm()
  selectedSendRecordId.value = ''
  if (props.triggerRecordId) {
    fetchTriggerSendRecords()
  }
}

// 监听触发记录ID变化
watch(() => props.triggerRecordId, (newId) => {
  if (newId && open.value) {
    fetchTriggerSendRecords()
  }
})

// 触发发送记录选择选项
// const triggerSendOptions = computed(() => {
//   return triggerSendRecords.value.map(record => ({
//     label: `发送记录 ${record.id} - ${record.expectedSendTime ? new Date(record.expectedSendTime).toLocaleString() : ''}`,
//     value: record.id,
//   }))
// })
</script>

<template>
  <Dialog v-model:visible="open" modal header="填写解决方案" :style="{ width: '50rem' }" @show="onShow">
    <form @submit.prevent="save">
      <div class="space-y-6">
        <!-- 触发记录ID显示 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">触发记录ID</label>
          <div class="border border-surface-300 rounded-md bg-surface-50 p-3 text-sm dark:border-surface-600 dark:bg-surface-800">
            {{ triggerRecordId || '未指定' }}
          </div>
        </div>

        <!-- 触发发送记录选择 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">
            触发发送记录 <span class="text-red-500">*</span>
          </label>
          <div v-if="loadingSendRecords" class="flex items-center gap-2 text-sm text-muted-color">
            <i class="pi pi-spin pi-spinner" />
            <span>加载发送记录中...</span>
          </div>
          <div v-else-if="triggerSendRecords.length === 0" class="text-sm text-muted-color">
            暂无触发发送记录
          </div>
          <!-- <LSelect
            v-else
            name="triggerSendId"
            :options="triggerSendOptions"
            placeholder="请选择触发发送记录"
          /> -->
        </div>

        <!-- 解决方案内容 -->
        <LTextarea
          name="solution"
          label="解决方案"
          :textarea-props="{
            rows: 6,
            placeholder: '请详细描述解决方案...',
            autoResize: true,
          }"
        />

        <!-- 解决时间 -->
        <LDatePicker
          name="solveTime"
          label="解决时间"
          :date-props="{
            showTime: true,
            hourFormat: '24',
            showSeconds: true,
          }"
        />

        <!-- 解决人信息显示 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">解决人</label>
          <div class="border border-surface-300 rounded-md bg-surface-50 p-3 text-sm dark:border-surface-600 dark:bg-surface-800">
            {{ userStore.user?.name || '未知用户' }} ({{ userStore.user?.id || '' }})
          </div>
        </div>
      </div>

      <div class="mt-8 flex justify-center gap-4">
        <Button
          type="submit"
          :loading="loading"
          :disabled="triggerSendRecords.length === 0"
          label="保存解决方案"
          icon="pi pi-check"
        />
        <Button
          type="button"
          label="取消"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="open = false"
        />
      </div>
    </form>
  </Dialog>
</template>
