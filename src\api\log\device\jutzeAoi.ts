interface Record {
  testlabel: string
  serialnumber: string
  softwareversion: string
  softwareversion1: string
  datetime: Date
  teststation: string
  errorcode: string
  testresult: string
  productcode: string
  componentid: string
  xcoordinate: number
  ycoordinate: number
  defecttype: string
  defectcode: number
  occurrence: number
  operator: string
  imagepath: string
}

export enum JutzeLogType {
  ERROR = 'error',
  OK = 'ok',
  NG = 'ng',
}

export interface JutzeAoiLog {
  id: string
  filename: string
  type: JutzeLogType
  logtime: Date
  record: Record[]
}
