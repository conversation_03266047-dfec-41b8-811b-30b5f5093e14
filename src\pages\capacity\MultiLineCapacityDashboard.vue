<script setup lang="ts">
import { format } from 'date-fns'
import type { DataTableCellEditCompleteEvent } from 'primevue/datatable'
import CapacityChart from './components/CapacityChart.vue'
import { capacityApi } from '~/api/capacity'
import type { CapacityReportWithLine, PlannedCapacityCreate } from '~/api/capacity/type'
import { unmetReasonApi } from '~/api/unmet-reason'
import type { UnmetReasonCreateParam } from '~/api/unmet-reason/type'
import LazyLoad from '~/components/common/LazyLoad.vue'
import PageContainer from '~/components/common/PageContainer.vue'

// 响应式数据
const selectedDate = ref<Date>(new Date())
const selectedInterval = ref(2)
const loading = ref(false)
const exportLoading = ref(false)

const lineData = ref<CapacityReportWithLine[]>()

// 弹出层相关
const showProductDialog = ref(false)
const selectedLineProducts = ref<any[]>([])

const intervalOptions = ref([
  { label: '1小时', value: 1 },
  { label: '2小时', value: 2 },
  { label: '4小时', value: 4 },
  { label: '8小时', value: 8 },
  { label: '12小时', value: 12 },
  { label: '24小时', value: 24 },
])

// 刷新数据
async function refreshData() {
  loading.value = true
  try {
    const dateStr = format(selectedDate.value, 'yyyy-MM-dd')
    // 调用实际的API
    const res = await capacityApi.getReport(dateStr, selectedInterval.value)

    lineData.value = res
  }
  catch (error) {
    console.error('获取数据失败:', error)
  }
  finally {
    loading.value = false
  }
}

async function exportData() {
  exportLoading.value = true
  try {
    await handleFileExport(
      () => capacityApi.exportReport(format(selectedDate.value, 'yyyy-MM-dd'), selectedInterval.value),
      '产能报表.xlsx',
    )
  }
  catch (error) {
    console.error('导出过程中发生错误:', error)
  }
  finally {
    exportLoading.value = false
  }
}

async function onCellEditComplete(event: DataTableCellEditCompleteEvent, lineCode: string) {
  const { data, newValue, field } = event
  if (field === 'reason' && newValue) {
    const param: UnmetReasonCreateParam = {
      lineId: lineCode,
      reason: newValue,
      startWorkTime: data.startWorkTime,
      endWorkTime: data.endWorkTime,
    }
    await unmetReasonApi.create(param)
    refreshData()
  }
}

async function updatePlannedQuantity(event: DataTableCellEditCompleteEvent) {
  const { data, newValue, field } = event
  if (field === 'plannedQuantity' && newValue) {
    const param: PlannedCapacityCreate = {
      lineCode: data.lineCode,
      productModel: data.productModel,
      plannedQuantity: newValue,
    }
    await capacityApi.create(param)
    refreshData()
  }
}

// 显示生产产品弹出层
function showProducts(products: any[]) {
  selectedLineProducts.value = products
  showProductDialog.value = true
}

// 组件挂载时加载数据
onMounted(() => {
  refreshData()
})
</script>

<template>
  <PageContainer :class="{ light: !isDark, dark: isDark }">
    <!-- 页面标题 -->
    <div class="mb-6 text-center">
      <h1 class="text-2xl text-primary font-bold">
        多线产能看板
      </h1>
      <!-- <p class="mt-2 text-muted-color">
        Multi-Line Capacity Dashboard
      </p> -->
    </div>

    <!-- 筛选器 -->
    <div class="mb-6 flex items-center justify-center gap-4 border border-surface-border rounded-lg bg-surface-800 p-4 light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow">
      <label class="font-medium">日期:</label>
      <DatePicker
        v-model="selectedDate"
        date-format="yy-mm-dd"

        fluid show-icon
        class="w-48"
      />

      <label class="font-medium">间隔:</label>
      <Dropdown
        v-model="selectedInterval"
        :options="intervalOptions"
        option-label="label"
        option-value="value"
        placeholder="选择间隔"
        class="w-32"
      />

      <Button
        label="刷新全部"
        icon="pi pi-refresh"
        :loading="loading"
        @click="refreshData"
      />

      <Button
        label="导出"
        icon="pi pi-download"
        :loading="exportLoading"
        @click="exportData"
      />
    </div>

    <!-- 加载指示器 -->
    <div v-if="loading" class="flex items-center justify-center py-10">
      <ProgressSpinner />
    </div>

    <!-- 产线报表容器 -->
    <div v-else-if="lineData && lineData.length > 0" class="space-y-8">
      <div
        v-for="lineReport in lineData"
        :key="lineReport.lineCode"
        class="border border-surface-border rounded-lg bg-surface-800 p-6 shadow-sm light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
      >
        <!-- 产线标题 -->
        <h2 class="mb-6 border-b border-surface-border pb-3 text-xl text-primary font-semibold">
          产线: {{ lineReport.lineCode }}
        </h2>

        <LazyLoad min-height="600px">
          <!-- 图表 -->
          <div class="mb-6 h-80">
            <CapacityChart
              :line-id="lineReport.lineCode"
              :data="lineReport.capacityReportDtos"
            />
          </div>

          <!-- 生产产品按钮 -->
          <div v-if="lineReport.plannedCapacityInfos && lineReport.plannedCapacityInfos.length > 0" class="mb-6">
            <Button
              label="查看生产产品"
              icon="pi pi-list"
              @click="showProducts(lineReport.plannedCapacityInfos)"
            />
          </div>

          <div class="grid grid-cols-2 my-6 gap-4 rounded-lg bg-surface-700 p-4 text-center md:grid-cols-5 light:bg-surface-100">
            <div>
              <p class="text-sm text-muted-color">
                总计划产能
              </p>
              <p class="text-xl font-semibold">
                {{ lineReport.lineSummary.totalPlannedQuantity }} <span class="text-sm">pcs</span>
              </p>
            </div>
            <!-- <div>
              <p class="text-sm text-muted-color">
                总标准产能
              </p>
              <p class="text-xl font-semibold">
                {{ lineReport.lineSummary.totalTheoreticalQuality }} <span class="text-sm">pcs</span>
              </p>
            </div> -->
            <div>
              <p class="text-sm text-muted-color">
                总实际产能
              </p>
              <p class="text-xl font-semibold">
                {{ lineReport.lineSummary.totalActualQuantity }} <span class="text-sm">pcs</span>
              </p>
            </div>
            <div>
              <p class="text-sm text-muted-color">
                总达成率
              </p>
              <p class="text-xl font-semibold">
                {{ (lineReport.lineSummary.totalAchievementRate * 100).toFixed(2) }}%
              </p>
            </div>
            <div>
              <p class="text-sm text-muted-color">
                达成时段数
              </p>
              <p class="text-xl text-green-500 font-semibold">
                {{ lineReport.lineSummary.metCount }}
              </p>
            </div>
            <div>
              <p class="text-sm text-muted-color">
                未达成时段数
              </p>
              <p class="text-xl text-red-500 font-semibold">
                {{ lineReport.lineSummary.notMetCount }}
              </p>
            </div>
          </div>

          <!-- 产能数据表格 -->
          <div class="mb-6">
            <h3 class="mb-3 text-lg text-primary font-semibold">
              产能数据
            </h3>
            <DataTable
              :value="lineReport.capacityReportDtos"
              class="mt-4"
              edit-mode="cell"
              responsive-layout="scroll"
              :virtual-scroller-options="{ itemSize: 46 }"
              scrollable
              striped-rows scroll-height="400px"
              @cell-edit-complete="(event) => onCellEditComplete(event, lineReport.lineCode)"
            >
              <Column field="startWorkTime" header="开始时间" />
              <Column field="endWorkTime" header="结束时间" />
              <!-- <Column field="workDate" header="工作日期" />
              <Column field="hour" header="小时 (h)">
                <template #body="{ data }">
                  {{ data.hour }}:00-{{ (data.hour + 1) % 24 }}:00
                </template>
              </Column> -->
              <!-- <Column field="plannedQuantity" header="计划产能 (pcs)" /> -->
              <Column field="theoreticalQuantity" header="标准产能 (pcs)" />
              <Column field="actualQuantity" header="实际产能 (pcs)" />
              <Column field="achievementRate" header="达成率 (%)" />
              <Column field="status" header="状态">
                <template #body="{ data }">
                  <Tag
                    :value="data.status"
                    :severity="data.status === '达成' ? 'success' : 'danger'"
                  />
                </template>
              </Column>
              <Column field="reason" header="未达成原因">
                <template #body="{ data }">
                  {{ data.reason }}
                </template>
                <template #editor="{ data, field }">
                  <InputText v-model="data[field]" fluid autofocus />
                </template>
              </Column>
            </DataTable>
          </div>
        </LazyLoad>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-else class="py-10 text-center">
      <p>没有可显示的数据。</p>
    </div>

    <!-- 生产产品弹出层 -->
    <Dialog
      v-model:visible="showProductDialog"
      modal
      header="生产产品信息"
      :style="{ width: '50rem' }"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
    >
      <DataTable
        :value="selectedLineProducts"
        responsive-layout="scroll"
        striped-rows
        scrollable
        scroll-height="400px"
        edit-mode="cell"
        @cell-edit-complete="(event) => updatePlannedQuantity(event)"
      >
        <Column field="trackName" header="轨道名称" />
        <Column field="lineCode" header="产线代码" />
        <Column field="productModel" header="产品型号" />
        <Column field="plannedQuantity" header="计划产量 (pcs/h)">
          <template #body="{ data }">
            <span class="font-medium">{{ data.plannedQuantity }}</span>
          </template>
          <template #editor="{ data, field }">
            <InputText v-model="data[field]" fluid autofocus />
          </template>
        </Column>
      </DataTable>
    </Dialog>
  </PageContainer>
</template>
