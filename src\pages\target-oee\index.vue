<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { useConfirm } from 'primevue/useconfirm'
import Create from './create.vue'
import Edit from './edit.vue'
import type { PageData } from '~/api/common/type'
import { targetOeeApi } from '~/api/target-oee'
import type { TargetOee } from '~/api/target-oee/type'

const loading = ref<boolean>(false)
const editId = ref<string>()
const open = reactive({
  create: false,
  edit: false,
})
const data = ref<TargetOee[]>([])
const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})
const total = ref(0)

async function loadData() {
  try {
    loading.value = true
    const res = await targetOeeApi.page({
      pageData,
      searchParams: {},
    })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
}

const confirm = useConfirm()
function confirmDel(id: string, event: any) {
  confirm.require({
    target: event.currentTarget,
    message: '确认删除？',
    group: 'delete',
    accept: async () => {
      await targetOeeApi.delete(id)
      success('删除成功')
      data.value = data.value.filter(o => o.id !== id)
    },
  })
}

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  loadData()
}

// 打开创建页面
function openCreatePage() {
  open.create = true
}

// 打开编辑页面
function openEditPage(id: string) {
  open.edit = true
  editId.value = id
}

onMounted(() => {
  loadData()
})
</script>

<template>
  <PageContainer class="mt-4">
    <ButtonGroup class="pl-8">
      <Button outlined icon="pi pi-plus" @click="openCreatePage()" />
    </ButtonGroup>
    <DataTable
      class="p-4"
      :value="data"
      lazy
      paginator
      data-key="id"
      :rows="pageData.pageSize"
      :total-records="total"
      @page="page"
    >
      <Column field="availability" header="稼动率" />
      <Column field="performance" header="运行效率" />
      <Column field="quality" header="良品率" />
      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined icon="pi pi-pencil" @click="openEditPage(slotProps.data.id)" />
            <Button outlined severity="danger" icon="pi pi-trash" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column>
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
    <Create v-model:open="open.create" @save="loadData" />
    <Edit :id="editId" v-model:open="open.edit" @save="loadData" />
  </PageContainer>
</template>
