import toasteventbus from 'primevue/toasteventbus'

interface ToastMsg {
  summary: string
  detail?: string
  life?: number
}
export function infoToast(msg: ToastMsg) {
  toasteventbus.emit('add', { severity: 'info', life: 3000, ...msg })
}

export function warnToast(msg: ToastMsg) {
  toasteventbus.emit('add', { severity: 'warn', life: 3000, ...msg })
}

export function successToast(msg: ToastMsg) {
  toasteventbus.emit('add', { severity: 'success', life: 3000, ...msg })
}

export function errorToast(msg: ToastMsg) {
  toasteventbus.emit('add', { severity: 'error', life: 3000, ...msg })
}

export function info(msg: string) {
  infoToast({
    summary: msg,
  })
}

export function warn(msg: string) {
  warnToast({
    summary: msg,
  })
}
export function success(msg: string) {
  successToast({
    summary: msg,
  })
}
export function error(msg: string) {
  errorToast({
    summary: msg,
  })
}

export function clearToast() {
  toasteventbus.emit('clear')
}
