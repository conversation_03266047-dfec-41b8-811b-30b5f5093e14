<script setup lang="ts">
import { formatDate } from '@vueuse/core'
import { HTTPError } from 'ky'
import { logApi } from '~/api/log'
import type { GkgPrintLog } from '~/api/log/device/gkgPrint'

const props = defineProps<{
  deviceCode: string
  id: string
}>()

const data = ref<GkgPrintLog>()

watch(() => props, async () => {
  search()
})

async function search() {
  try {
    const res = await logApi.getProductionLog({
      deviceCode: props.deviceCode,
      id: props.id,
    })
    data.value = res
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(ex.message)
    }
  }
}

onMounted(() => {
  search()
})
</script>

<template>
  <div class="w-full p-4">
    <div class="mb-4 w-full flex justify-center text-2xl font-bold">
      GKG Printer Log
    </div>
    <Panel header="基本信息" class="mb-4 w-full">
      <div class="grid grid-cols-8 gap-4 p-4">
        <div> 日志名称 </div><div>{{ data?.filename }}</div>
        <div>日志时间</div><div>{{ data?.logtime ? formatDate(data?.logtime, 'YYYY-MM-DD HH:mm:ss') : '' }}</div>
        <div>楼层</div><div>{{ data?.index.floor }}</div>
        <div>产线</div><div>{{ data?.index.line }}</div>
        <div>机器类型</div><div>{{ data?.index.machinetype }}</div>
        <div>供应商</div><div>{{ data?.index.vendor }}</div>
        <div>机器ID</div><div>{{ data?.index.machineid }}</div>
        <div>机器序列号</div><div>{{ data?.index.machinesn }}</div>
      </div>
    </Panel>
    <Panel header="记录" class="mb-4 w-full">
      <DataTable :value="data?.record" :rows="10" scrollable paginator scroll-height="800px">
        <Column field="tag" header=" 标签" />
        <Column field="timestamp" header=" 时间戳" />
        <Column field="machinename" header=" 机器名称" />
        <Column field="barcode" header=" 条形码" />
        <Column field="filename" header=" 文件名" />
        <Column field="programrunning" header=" 程序运行状态" />
        <Column field="pcbnumber" header=" PCB编号" />
        <Column field="prodname" header=" 产品名称" />
        <Column field="prodtype" header=" 产品类型" />
        <Column field="cycletime" header=" 周期时间" />
        <Column field="spiresult2d" header=" 2D锡膏检测结果" />
        <Column field="printspeed" header=" 印刷速度" />
        <Column field="frontsqgpress" header=" 前刮刀压力" />
        <Column field="rearsqgpress" header=" 后刮刀压力" />
        <Column field="printmode" header=" 印刷模式" />
        <Column field="printgap" header=" 印刷间隙" />
        <Column field="snapoffdistance" header=" 分离距离" />
        <Column field="snapoffspeed" header=" 分离速度" />
        <Column field="snapoffdelay" header=" 分离延时" />
        <Column field="sqgupspeed" header=" 刮刀上升速度" />
        <Column field="sqgdownspeed" header=" 刮刀下降速度" />
        <Column field="sqgupfirst" header=" 刮刀优先上升" />
        <Column field="sqgheightatsnapoff" header=" 分离时刮刀高度" />
        <Column field="cleaningafterlastboard" header=" 最后一块板后清洗" />
        <Column field="cleaningfrequency" header=" 清洗频率" />
        <Column field="cleaningspeed" header=" 清洗速度" />
        <Column field="cleaningmode" header=" 清洗模式" />
        <Column field="cleaningtype" header=" 清洗类型" />
        <Column field="addspmode" header=" 加锡膏模式" />
        <Column field="addspafterlastboard" header=" 最后一块板后加锡膏" />
        <Column field="spiresult3d" header=" 3D锡膏检测结果" />
        <Column field="printdirection" header=" 印刷方向" />
        <Column field="pcbsize" header=" PCB尺寸" />
        <Column field="tableupx" header=" 台面上升X位置" />
        <Column field="tableupy1" header=" 台面上升Y1位置" />
        <Column field="tableupy2" header=" 台面上升Y2位置" />
        <Column field="xforward" header=" X前进" />
        <Column field="y1forward" header=" Y1前进" />
        <Column field="y2forward" header=" Y2前进" />
        <Column field="xbackward" header=" X后退" />
        <Column field="y1backward" header=" Y1后退" />
        <Column field="y2backward" header=" Y2后退" />
        <Column field="temperature" header=" 温度" />
        <Column field="humidity" header=" 湿度" />
        <Column field="markdeviation" header=" 标记偏差" />
        <Column field="tabletoprintingpos" header=" 台面到印刷位置" />
        <Column field="tabletosnapoffpos" header=" 台面到分离位置" />
      </DataTable>
    </Panel>
    <Panel header="状态" class="w-full">
      <DataTable :value="data?.status" paginator :rows="10">
        <Column field="barcode" header="条形码" />
        <Column field="machinename" header="机器名称" />
        <Column field="status" header="状态" />
        <Column field="starttime" header="开始时间" />
        <Column field="endtime" header="结束时间" />
        <Column field="describe" header="描述" />
        <Column field="value" header="值" />
        <Column field="value1" header="值1" />
        <Column field="value2" header="值2" />
        <Column field="value3" header="值3" />
        <Column field="value4" header="值4" />
        <Column field="value5" header="值5" />
      </DataTable>
    </Panel>
  </div>
</template>
