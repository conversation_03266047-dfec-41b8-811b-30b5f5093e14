import type { PageList, Pageable } from '../type'
import type { Dict, DictCreateParam, DictSimple, DictSimpleWithId, DictUpdateParam } from './types'
import { kyDelete, kyGet, kyPost } from '~/utils/request'

export const dictApi = {
  create: (param: DictCreateParam) => kyPost('dict/create', param),
  page: (param: Pageable<Partial<DictSimple>>) => kyPost('dict/page', param).json<PageList<DictSimpleWithId>>(),
  update: (id: string, param: DictUpdateParam) => kyPost(`dict/update/${id}`, param),
  delete: (id: string) => kyDelete(`dict/${id}`),
  get: (id: string) => kyGet(`dict/${id}`).json<Dict>(),
  getByCode: (code: string) => kyGet(`dict/getByCode/${code}`).json<Dict>(),
}
