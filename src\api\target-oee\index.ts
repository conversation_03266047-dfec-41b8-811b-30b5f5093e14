import type { PageList, Pageable } from '../common/type'
import type { TargetOee, TargetOeeCreateParam } from './type'
import { kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const targetOeeApi = {
  create: (param: TargetOeeCreateParam) => kyPost('target-oee', param),
  page: (param: Pageable<Partial<TargetOee>>) => kyPost('target-oee/page', param).json<PageList<TargetOee>>(),
  delete: (id: string) => kyDelete(`target-oee/${id}`),
  get: (id: string) => kyGet(`target-oee/${id}`).json<TargetOee>(),
  update: (id: string, param: TargetOeeCreateParam) => kyPut(`target-oee/${id}`, param),
}
