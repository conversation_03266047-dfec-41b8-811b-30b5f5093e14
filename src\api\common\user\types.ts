export interface UserCreate {
  name: string
  displayName: string
  avatar?: string
  password: string
}

export interface UserView {
  id: string
  name: string
  avatar: string
  displayName: string
  roles: string[]
  perms: string[]
  locked: boolean
}

export interface UserUpdate {
  displayName: string
  avatar?: string
  roles: string[]
  perms: string[]
}

export interface UserQuery {
  name?: string
  displayName?: string
}
