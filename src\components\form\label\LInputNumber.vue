<script setup lang="ts">
import type { InputNumberProps } from 'primevue/inputnumber'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
  inputProps?: InputNumberProps
}>()

// 设置默认的 inputProps，确保允许小数点输入
const defaultProps: Partial<InputNumberProps> = {
  mode: 'decimal',
  minFractionDigits: 0,
  maxFractionDigits: 2,
}

const finalInputProps = {
  ...defaultProps,
  ...props.inputProps,
}

const { value, errorMessage } = useField<number | null | undefined>(() => props.name)
</script>

<template>
  <LabelFormItem :label="label" :name="name">
    <InputNumber
      :id="props.name"
      v-model="value"
      :invalid="!!errorMessage"
      v-bind="finalInputProps"
    />
    <ErrorMsg :error-message="errorMessage" />
  </LabelFormItem>
</template>
