import type { PageList, Pageable } from '../common/type'
import type { NpmEventCode, NpmEventCodeCreateParam } from './types'
import { kyDelete, kyGet, kyPost, kyPostFile, kyPut } from '~/utils/request'

export const npmEventCodeApi = {
  create: (param: NpmEventCodeCreateParam) => kyPost('npm-event-code', param),
  page: (param: Pageable<Partial<NpmEventCode>>) => kyPost('npm-event-code/page', param).json<PageList<NpmEventCode>>(),
  delete: (id: string) => kyDelete(`npm-event-code/${id}`),
  get: (id: string) => kyGet(`npm-event-code/${id}`).json<NpmEventCode>(),
  update: (id: string, param: NpmEventCodeCreateParam) => kyPut(`npm-event-code/${id}`, param),
  export: (param: Partial<NpmEventCode>) => kyPost('npm-event-code/export', param),
  import: (formData: FormData) => kyPostFile('npm-event-code/import', formData),
}
