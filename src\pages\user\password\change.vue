<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { HTTPError } from 'ky'
import Button from 'primevue/button'
import Password from 'primevue/password'
import InputGroup from 'primevue/inputgroup'
import InputGroupAddon from 'primevue/inputgroupaddon'
import { Field, useForm } from 'vee-validate'
import { z } from 'zod'
import { useRouter } from 'vue-router' // 新增
import { userApi } from '~/api/common/user'

const open = defineModel<boolean>('open')
const loading = ref(false)
const router = useRouter() // 新增

const schema = toTypedSchema(
  z.object({
    oldPassword: z.string({
      required_error: '必填',
    }).min(1, '请输入原密码'),
    newPassword: z.string({
      required_error: '必填',
    }).min(6, '新密码至少6位'),
    confirmPassword: z.string(
      {
        required_error: '必填',
      },
    ).min(1, '请确认新密码'),
  }).refine(data => data.newPassword === data.confirmPassword, {
    message: '两次输入的密码不一致',
    path: ['confirmPassword'],
  }),
)

const { handleSubmit, resetForm } = useForm({
  validationSchema: schema,
})

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await userApi.changePassword(values.oldPassword, values.newPassword)
    success('密码修改成功')
    open.value = false
    resetForm()
    router.push('/login') // 新增：跳转到登录页
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(`修改失败：${ex.message}`)
    }
  }
  finally {
    loading.value = false
  }
})
</script>

<template>
  <div>
    <h2 class="mb-6 text-center text-xl font-bold">
      修改密码
    </h2>
    <form
      class="mx-auto max-w-md border rounded-xl p-8 shadow-md"
      @submit.prevent="save"
    >
      <div class="mb-6">
        <label class="mb-2 block font-medium">原密码</label>
        <InputGroup>
          <InputGroupAddon>
            <i class="pi pi-lock" />
          </InputGroupAddon>
          <Field
            v-slot="{ field, errorMessage }"
            name="oldPassword"
          >
            <Password v-bind="field" toggle-mask :feedback="false" input-class="w-full" placeholder="请输入原密码" />
            <small v-if="errorMessage" class="p-error mt-1 block">{{ errorMessage }}</small>
          </Field>
        </InputGroup>
      </div>
      <div class="mb-6">
        <label class="mb-2 block font-medium">新密码</label>
        <InputGroup>
          <InputGroupAddon>
            <i class="pi pi-key" />
          </InputGroupAddon>
          <Field
            v-slot="{ field, errorMessage }"
            name="newPassword"
          >
            <Password v-bind="field" toggle-mask :feedback="false" input-class="w-full" placeholder="请输入新密码" />
            <small v-if="errorMessage" class="p-error mt-1 block">{{ errorMessage }}</small>
          </Field>
        </InputGroup>
      </div>
      <div class="mb-8">
        <label class="mb-2 block font-medium">确认密码</label>
        <InputGroup>
          <InputGroupAddon>
            <i class="pi pi-check" />
          </InputGroupAddon>
          <Field
            v-slot="{ field, errorMessage }"
            name="confirmPassword"
          >
            <Password v-bind="field" toggle-mask :feedback="false" input-class="w-full" placeholder="请再次输入新密码" />
            <small v-if="errorMessage" class="p-error mt-1 block">{{ errorMessage }}</small>
          </Field>
        </InputGroup>
      </div>
      <div class="flex justify-center gap-8">
        <Button type="submit" :loading="loading" label="保存" icon="pi pi-save" />
        <Button severity="secondary" label="重置" icon="pi pi-refresh" @click="resetForm()" />
      </div>
    </form>
  </div>
</template>
