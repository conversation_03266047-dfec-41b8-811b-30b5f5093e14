<script setup lang="ts">
import type { SelectButtonProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
  options: any[]
  selectProps?: SelectButtonProps
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <LabelFormItem :name="name" :label="label">
    <SelectButton v-model="value" :input-id="props.name" :options="options" option-label="label" option-value="value" :invalid="errorMessage ? true : false" fluid v-bind="selectProps" />
  </LabelFormItem>
</template>
