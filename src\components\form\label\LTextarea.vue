<script setup lang="ts">
import type { TextareaProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
  textareaProps?: TextareaProps
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <LabelFormItem :label="label" :name="name">
    <Textarea :id="props.name" v-model.trim="value" :invalid="errorMessage ? true : false" fluid v-bind="textareaProps" />
    <ErrorMsg :error-message="errorMessage" />
  </LabelFormItem>
</template>
