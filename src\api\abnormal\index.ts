import type { PageList, Pageable } from '../common/type'
import type { Abnormal, AbnormalConfirmParam, AbnormalCreateParam, AbnormalEditParam, AbnormalSearchParam } from './types'
import { kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const abnormalApi = {
  create: (param: AbnormalCreateParam) => kyPost('abnormal', param),
  page: (param: Pageable<Partial<AbnormalSearchParam>>) => kyPost('abnormal/page', param).json<PageList<Abnormal>>(),
  delete: (id: string) => kyDelete(`abnormal/${id}`),
  get: (id: string) => kyGet(`abnormal/${id}`).json<Abnormal>(),
  update: (id: string, param: AbnormalEditParam) => kyPut(`abnormal/${id}`, param),
  confirm: (id: string, param: AbnormalConfirmParam) => kyPut(`abnormal/${id}/confirm`, param),
  verified: (id: string) => kyPut(`abnormal/${id}/verified`),
}
