<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { useConfirm } from 'primevue/useconfirm'
import Create from './create.vue'
import Edit from './edit.vue'
import Import from './import.vue'
import { useTheoreticalSearchForm } from './schema'
import type { PageData } from '~/api/common/type'
import { theoreticalApi } from '~/api/theoretical'
import type { TheoreticalOutput } from '~/api/theoretical/types'
import { handleFileExport } from '~/utils/export'

const loading = ref<boolean>(false)
const editId = ref<string>()
const open = reactive({
  create: false,
  edit: false,
  import: false,
})
const data = ref<TheoreticalOutput[]>([])
const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})
const total = ref(0)
const searchForm = useTheoreticalSearchForm()
const search = searchForm.handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await theoreticalApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

const confirm = useConfirm()
function confirmDel(id: string, event: any) {
  confirm.require({
    target: event.currentTarget,
    message: '确认删除？',
    group: 'delete',
    accept: async () => {
      await theoreticalApi.delete(id)
      success('删除成功')
      data.value = data.value.filter(o => o.id !== id)
    },
  })
}

const selectedItems = ref<TheoreticalOutput[]>([])

// 添加批量删除方法
function confirmBatchDel(event: any) {
  if (!selectedItems.value.length) {
    warn('请选择要删除的项目')
    return
  }

  confirm.require({
    target: event.currentTarget,
    message: `确认删除选中的 ${selectedItems.value.length} 项？`,
    group: 'delete',
    accept: async () => {
      try {
        loading.value = true
        await theoreticalApi.batchDelete(selectedItems.value.map(item => item.id))
        success('批量删除成功')
        selectedItems.value = []
        search()
      }
      finally {
        loading.value = false
      }
    },
  })
}

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

// 打开创建页面
function openCreatePage() {
  open.create = true
}

// 打开编辑页面
function openEditPage(id: string) {
  open.edit = true
  editId.value = id
}

// 打开导入页面
function handleImport() {
  open.import = true
}

// 导出
async function handleExport() {
  await handleFileExport(
    () => theoreticalApi.export(searchForm.values),
    '理论产出数据.xlsx',
    isLoading => loading.value = isLoading,
  )
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer class="mt-4">
    <SearchBox :loading="loading" @submit="search" @search="search">
      <FInput name="lineCode" label="线体编码" />
      <FInput name="deviceCode" label="设备编码" />
      <FInput name="deviceType" label="设备类型" />
      <FInput name="productModel" label="产品型号" />
    </SearchBox>
    <ButtonGroup class="pl-8">
      <Button outlined icon="pi pi-plus" @click="openCreatePage()" />
      <Button outlined icon="pi pi-upload" @click="handleImport()" />
      <Button outlined icon="pi pi-download" @click="handleExport()" />
      <Button
        outlined
        severity="danger"
        icon="pi pi-trash"
        :disabled="!selectedItems.length"
        @click="confirmBatchDel($event)"
      />
    </ButtonGroup>
    <DataTable
      v-model:selection="selectedItems"
      class="p-4"
      :value="data"
      lazy
      paginator
      data-key="id"
      :rows="pageData.pageSize"
      :total-records="total"
      @page="page"
    >
      <Column selection-mode="multiple" :frozen="true" style="width: 3rem" />
      <Column field="lineCode" header="线体编码" />
      <Column field="deviceCode" header="设备编码" />
      <Column field="deviceType" header="设备类型" />
      <Column field="productModel" header="产品型号" />
      <Column field="ct" header="理论CT" />
      <Column field="flatNumber" header="拼板数" />
      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined icon="pi pi-pencil" @click="openEditPage(slotProps.data.id)" />
            <Button outlined severity="danger" icon="pi pi-trash" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column>
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
    <Create v-model:open="open.create" @save="search" />
    <Edit :id="editId" v-model:open="open.edit" @save="search" />
    <Import v-model:open="open.import" @refresh="search" />
  </PageContainer>
</template>

<style scoped>
.card {
  padding: 1.5rem;
  border-radius: 4px;
  background: var(--surface-card);
}
</style>
