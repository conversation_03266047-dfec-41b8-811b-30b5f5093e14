<script setup lang="ts">
import { useEventEditForm } from './schema'
import type { NpmEventCode } from '~/api/event/types'
import { npmEventCodeApi } from '~/api/event'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit, setValues } = useEventEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await npmEventCodeApi.update(props.id, values)
      success('创建成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const eventData: NpmEventCode = await npmEventCodeApi.get(props.id)
    setValues(eventData)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="编辑事件" @show="loadData">
    <form @submit="save">
      <FormLayout>
        <LInput name="mainCode" label="主代码" />
        <LInput name="subCode" label="子代码" />
        <LInput name="mainSubject" label="主题目" />
        <LInput name="subSubject" label="子题目" />
        <LInput name="eventType" label="事件类型" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
