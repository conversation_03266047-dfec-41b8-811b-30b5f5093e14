<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const products = ref([
  { id: '674fbd48c473d52997d1076f', name: 'KA-U201-25020340-V13-TB', startTime: '2024-06-05 04:59:36', endTime: '2024-06-05 05:56:46' },
  { id: '674fbd48c473d52997d10770', name: 'KA-RF-25021308-V03-TB', startTime: '2024-06-05 20:47:54', endTime: '2024-06-05 20:59:33' },
])

function onCodeClick(index: string) {
  router.push(`/admin/samsung/info?id=${index}`)
}
</script>

<template>
  <div class="dashboard-container">
    <!-- Header Title -->
    <div
      class="header grid-col-span-10 flex items-center justify-center border-2 border-[rgba(255,255,255,0.2)] bg-[rgba(255,255,255,0.1)] text-36px font-bold text-shadow-[2px_2px_4px_rgba(0,0,0,0.6)] shadow-[0_4px_8px_rgba(0,0,0,0.3)] transition-shadow"
    >
      LINE 304 Samsung
    </div>

    <div
      class="chart-box grid-col-start-1 grid-col-end-3 border-2 border-[rgba(255,255,255,0.2)] bg-[rgba(255,255,255,0.1)] p-15px shadow-[0_4px_8px_rgba(0,0,0,0.3)]"
    >
      <div class="chart chart1" />
    </div>

    <div
      class="grid-col-start-3 grid-col-end-7 border-2 border-[rgba(255,255,255,0.2)] bg-[rgba(255,255,255,0.1)] p-15px shadow-[0_4px_8px_rgba(0,0,0,0.3)]"
    >
      <div class="chart chart1" />
    </div>

    <!-- Right Chart -->
    <div
      class="chart-box grid-col-start-7 grid-col-end-11 border-2 border-[rgba(255,255,255,0.2)] bg-[rgba(255,255,255,0.1)] p-15px shadow-[0_4px_8px_rgba(0,0,0,0.3)]"
    >
      <div id="chart3" class="chart" />
    </div>

    <!-- Bottom Chart -->
    <div
      class="chart-box grid-col-start-1 grid-col-end-7 border-2 border-[rgba(255,255,255,0.2)] bg-[rgba(255,255,255,0.1)] p-15px shadow-[0_4px_8px_rgba(0,0,0,0.3)]"
    >
      <div class="w-full border-collapse overflow-hidden text-white shadow-[0_2px_8px_rgba(0,0,0,0.3)]">
        <div class="flex bg-[rgba(0,0,0,0.6)] p-y-1">
          <div class="flex-1 border-b-2 border-white/20 p-0.5 text-center font-bold">
            编号
          </div>
          <div class="flex-1 border-b-2 border-white/20 p-0.5 text-center font-bold">
            产品名称
          </div>
          <div class="flex-1 border-b-2 border-white/20 p-0.5 text-center font-bold">
            日志开始时间
          </div>
          <div class="flex-1 border-b-2 border-white/20 p-0.5 text-center font-bold">
            日志结束时间
          </div>
        </div>
        <div class="table-body">
          <div v-for="(product) in products" :key="product.id" class="table-row">
            <div class="flex-1 border-b border-white/10 p-0.5 text-center">
              <a class="text-[#ffecb3] no-underline transition-colors duration-300 hover:text-[#ffab40]" href="#" @click.prevent="onCodeClick(product.id)">
                {{ product.id }}
              </a>
            </div>
            <div class="flex-1 border-b border-white/10 p-0.5 text-center">
              {{ product.name }}
            </div>
            <div class="flex-1 border-b border-white/10 p-0.5 text-center">
              {{ product.startTime }}
            </div>
            <div class="flex-1 border-b border-white/10 p-0.5 text-center">
              {{ product.endTime }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="chart-box grid-col-start-7 grid-col-end-11 border-2 border-[rgba(255,255,255,0.2)] bg-[rgba(255,255,255,0.1)] p-15px shadow-[0_4px_8px_rgba(0,0,0,0.3)]"
    >
      <div id="chart5" class="chart" />
    </div>
  </div>
</template>

<style scoped>
dashboard-container {
  height: 100%;
  width: 100%;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  grid-template-rows: 80px 1.5fr 1fr;
  gap: 20px;
  background-image: url('~/assets/images/background_panel.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: #fff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
}

.header {
  transition:
    transform 0.3s,
    box-shadow 0.3s;
}

.header:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

.chart-box {
  transition:
    transform 0.3s,
    box-shadow 0.3s;
}

.chart-box:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

.table-row {
  display: flex;
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0.3s;
}

.table-row:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.05); /* Alternating row colors */
}

.table-row:hover {
  background-color: rgba(255, 255, 255, 0.2); /* Hover effect */
}
</style>
