import type { PageList, Pageable } from '../type'
import type { MenuItemCreateParam, MenuItemData, MenuItemUpdateParam, MenuSearchParam } from './types'
import { kyDelete, kyGet, kyPost } from '~/utils/request'

export const menuApi = {
  create: (param: MenuItemCreateParam) => kyPost('menu/create', param),
  getUserMenu: () => kyGet('menu/getUserMenu').json<MenuItemData[]>(),
  pageItem: (param: Pageable<MenuSearchParam>) => kyPost('menu/pageItem', param).json<PageList<MenuItemData>>(),
  delete: (id: string) => kyDelete(`menu/${id}`),
  get: (id: string) => kyGet(`menu/${id}`).json<MenuItemData>(),
  update: (id: string, param: MenuItemUpdateParam) => kyPost(`menu/update/${id}`, param),
}
