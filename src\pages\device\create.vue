<script setup lang="ts">
import { useDeviceCreateForm } from './schema'
import { deviceApi } from '~/api/device'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit } = useDeviceCreateForm()

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await deviceApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建设备" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="设备名称" />
        <LInput name="code" label="设备编码" />
        <LSelect name="type" label="设备类型" :options="deviceTypeOptions" />
        <LSelect name="category" label="设备类别" :options="deviceCategoryOptions" />
        <LInput name="track" label="轨道" />
        <LSelect name="lineId" label="产线" :options="lineOptions" />
        <LInput name="clientIp" label="客户端IP" />
        <LInput name="clientPort" label="客户端端口" />
        <LInputNumber name="sort" label="排序" />
        <LInputNumber name="group" label="分组" />
        <LSelect name="enable" label="状态" :options="enableOptions" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
  </Dialog>
</template>
