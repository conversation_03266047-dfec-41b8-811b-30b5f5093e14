import type { PageList, Pageable } from '../common/type'
import type { LogGetParam, LogRecord, LogSearchParam } from './types'
import { kyGet, kyPost } from '~/utils/request'

export const logApi = {
  pageProductionLog: (param: Pageable<LogSearchParam>) => kyPost('log/production/page', param).json <PageList<LogRecord>>(),
  getProductionLog: (param: LogGetParam) => kyGet('log/production', param).json<any>(),
}
