<script setup lang="ts">
import { onBeforeMount, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import type { MenuItemExt } from './menu'
import { useMenuStore } from '~/stores/menu'

const props = withDefaults(defineProps<{
  item: MenuItemExt
  index: number
  root: boolean
  parentItemKey?: string | null
}>(), {
  parentItemKey: null,
  root: false,
})

const route = useRoute()

const { setActiveMenuItem, onMenuToggle } = useMenuStore()
const { menuState } = storeToRefs(useMenuStore())
const isActiveMenu = ref(false)
const itemKey = ref<string | null>(null)

onBeforeMount(() => {
  itemKey.value = props.parentItemKey ? `${props.parentItemKey}-${props.index}` : String(props.index)

  const activeMenuItemKey = menuState.value.activeMenuItemKey

  isActiveMenu.value = activeMenuItemKey === itemKey.value || activeMenuItemKey ? activeMenuItemKey.startsWith(`${itemKey.value}-`) : false
})

watch(
  () => menuState.value.activeMenuItemKey,
  (newVal) => {
    isActiveMenu.value = newVal === itemKey.value || newVal?.startsWith(`${itemKey.value}-`) || false
  },
)

function itemClick(event: MouseEvent, item: MenuItemExt) {
  if (item.disabled) {
    event.preventDefault()
    return
  }

  if ((item.path || item.url) && (menuState.value.staticMenuMobileActive || menuState.value.overlayMenuActive)) {
    onMenuToggle()
  }

  if (item.command) {
    item.command({ originalEvent: event, item })
  }

  const foundItemKey = item.items ? (isActiveMenu.value ? props.parentItemKey : itemKey) : itemKey.value

  setActiveMenuItem(foundItemKey, item)
}

function checkActiveRoute(item: MenuItemExt) {
  return route.path === item.path
}
</script>

<template>
  <li :class="{ 'layout-root-menuitem': root, 'active-menuitem': isActiveMenu }">
    <div v-if="root " class="layout-menuitem-root-text">
      {{ item.label }}
    </div>
    <a v-if="(!item.path || item.items) " :href="item.url" :class="item.class" :target="item.target" tabindex="0" @click="itemClick($event, item)">
      <i :class="item.icon" class="pi layout-menuitem-icon pi-fw" />
      <span class="layout-menuitem-text">{{ item.label }}</span>
      <i v-if="item.items" class="pi pi-fw layout-submenu-toggler pi-angle-down" />
    </a>
    <router-link v-if="item.path && !item.items " :class="[item.class, { 'active-route': checkActiveRoute(item) }]" tabindex="0" :to="item.path" @click="itemClick($event, item)">
      <i :class="item.icon" class="pi pi-fw layout-menuitem-icon" />
      <span class="layout-menuitem-text">{{ item.label }}</span>
      <i v-if="item.items" class="pi pi-fw pi-angle-down layout-submenu-toggler" />
    </router-link>
    <Transition v-if="item.items " name="layout-submenu">
      <ul v-show="root ? true : isActiveMenu" class="layout-submenu">
        <app-menu-item v-for="(child, i) in item.items" :key="child.key" :index="i" :item="child" :parent-item-key="itemKey" :root="false" />
      </ul>
    </Transition>
  </li>
</template>
