import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'

import type { DictSearchParam } from '~/api/common/dict/types'

const dictSchema = toTypedSchema(
  z.object({
    code: z.string().min(1),
    name: z.string().min(1),
    itemList: z.array(z.object({
      label: z.string().min(1),
      value: z.string().min(1),
    })).default([]),
  }),
)

const dictSearchSchema = toTypedSchema(
  z.object({
    code: z.string().optional(),
    name: z.string().optional(),
  }),
)

export function useDictForm() {
  const form = useForm({
    validationSchema: dictSchema,
  })
  return form
}

export function useDictSearchForm() {
  const form = useForm<DictSearchParam>({
    validationSchema: dictSearchSchema,
  })
  return form
}
