import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'

import type { MenuItemUpdateParam } from '~/api/common/menu/types'
import { TargetEnum } from '~/api/common/menu/types'

export const menuItemSchema = toTypedSchema(z.object({
  path: z.string().min(1, '请输入路径'),
  title: z.string().min(1, '请输入标题'),
  icon: z.string().optional(),
  target: z.nativeEnum(TargetEnum).default(TargetEnum.self),
  access: z.string().optional(),
  parentId: z.string().optional(),
  url: z.string().optional(),
  order: z.number().int().default(0),
  redirect: z.boolean().default(false),
}))

export const targetOptions = [
  {
    label: '_blank',
    value: '_blank',
  },
  {
    label: '_self',
    value: '_self',
  },
  {
    label: '_parent',
    value: '_parent',
  },
]

export function useCreateMenuItemForm() {
  const form = useForm({
    validationSchema: menuItemSchema,
  })
  return {
    ...form,
  }
}

export function useEditMenuItemForm() {
  const form = useForm<MenuItemUpdateParam>({
    validationSchema: menuItemSchema,
  })
  return {
    ...form,
  }
}
