<script setup lang="ts">
import { formatDate } from '@vueuse/core'
import type { DataTablePageEvent } from 'primevue/datatable'
import { useConfirm } from 'primevue/useconfirm'
import Create from './create.vue'
import Edit from './edit.vue'
import { useAbnormalSearchForm } from './schema'
import { abnormalApi } from '~/api/abnormal'
import type { Abnormal } from '~/api/abnormal/types'
import type { PageData } from '~/api/common/type'

const loading = ref<boolean>(false)
const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})
const data = ref<Abnormal[]>([])
const total = ref(0)
const open = reactive({
  confirm: false,
  create: false,
  edit: false,
})
const editId = ref<string>()
const searchForm = useAbnormalSearchForm()
const search = searchForm.handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await abnormalApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

const confirm = useConfirm()
function confirmDel(id: string, event: any) {
  confirm.require({
    target: event.currentTarget,
    message: '确认删除？',
    group: 'delete',
    accept: async () => {
      await abnormalApi.delete(id)
      success('删除成功')
      data.value = data.value.filter(o => o.id !== id)
    },
  })
}

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

// 打开创建页面
function openCreatePage() {
  open.create = true
}

// 打开编辑页面
function openEditPage(id: string) {
  open.edit = true
  editId.value = id
}

// 打开确认页面
function openConfirmPage(id: string, event: any) {
  // 校验解决方案是否填写
  const abnormal = data.value.find(o => o.id === id) // 获取当前异常信息
  if (!abnormal?.solution) {
    error('请先填写解决方案')
    return
  }

  confirm.require({
    target: event.currentTarget,
    message: '异常确认？',
    group: 'confirm',
    accept: async () => {
      await abnormalApi.verified(id)
      success('确认成功')
      search()
    },
  })
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer class="mt-4 text-sm">
    <SearchBox :loading="loading" @submit="search" @search="search">
      <FDictSelect name="lineCode" label="线体编码" code="LINE_CODE" />
      <FDictSelect name="classification" label="异常分类" code="ABNORMAL_CODE" />
      <FInput name="cause" label="异常原因" />
      <FInput name="responsible" label="责任人" />
      <FInput name="department" label="责任部门" />
      <FInput name="writer" label="填写人" />
      <FInput name="confirm" label="确认人" />
      <FDatePicker name="writeStartTime" label="填写开始时间" :date-props="{ showTime: true, showSeconds: true }" />
      <FDatePicker name="writeEndTime" label="填写结束时间" :date-props="{ showTime: true, showSeconds: true }" />
      <FDatePicker name="confirmStartTime" label="确认开始时间" :date-props="{ showTime: true, showSeconds: true }" />
      <FDatePicker name="confirmEndTime" label="确认结束时间" :date-props="{ showTime: true, showSeconds: true }" />
    </SearchBox>
    <ButtonGroup class="pl-8">
      <!-- <Button outlined icon="pi pi-plus" @click="openCreatePage()" /> -->
      <Button outlined label="创建异常" @click="openCreatePage()" />
    </ButtonGroup>
    <DataTable
      class="p-4" :value="data" lazy paginator data-key="id" :rows="pageData.pageSize" :total-records="total"
      @page="page"
    >
      <Column field="lineCode" header="线体编码" />
      <Column field="classification" header="异常分类">
        <template #body="slotProps">
          {{ formatDict(slotProps.data.classification, 'ABNORMAL_CODE') }}
        </template>
      </Column>
      <Column field="cause" header="异常原因" />
      <Column field="startTime" header="开始时间">
        <template #body="slotProps">
          {{ formatDate(slotProps.data.startTime, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
      </Column>
      <Column field="endTime" header="结束时间">
        <template #body="slotProps">
          {{ formatDate(slotProps.data.endTime, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
      </Column>
      <Column field="duration" header="异常时长">
        <template #body="slotProps">
          {{ (slotProps.data.duration / 60000).toFixed(2) }}
        </template>
      </Column>
      <Column field="solution" header="解决方案" />
      <Column field="responsible" header="责任人" />
      <Column field="department" header="责任部门" />
      <Column field="writer" header="填写人" />
      <Column field="writeTime" header="填写时间">
        <template #body="slotProps">
          {{ slotProps.data.writeTime ? formatDate(slotProps.data.writeTime, 'YYYY-MM-DD HH:mm:ss') : '' }}
        </template>
      </Column>
      <Column field="confirm" header="确认人" />
      <Column field="confirmTime" header="确认时间">
        <template #body="slotProps">
          {{ slotProps.data.confirmTime ? formatDate(slotProps.data.confirmTime, 'YYYY-MM-DD HH:mm:ss') : '' }}
        </template>
      </Column>
      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button
              v-if="!slotProps.data.confirm" outlined icon="pi pi-pencil"
              @click="openEditPage(slotProps.data.id)"
            />
            <Button
              v-if="!slotProps.data.confirm" outlined
              severity="danger" icon="pi pi-trash" @click="confirmDel(slotProps.data.id, $event)"
            />
            <Button
              v-if="!slotProps.data.confirm"
              v-permission="slotProps.data.classification"
              outlined icon="pi pi-check"
              @click="openConfirmPage(slotProps.data.id, $event)"
            />
          </div>
        </template>
      </Column>
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
    <Create v-model:open="open.create" @save="search" />
    <Edit :id="editId" v-model:open="open.edit" @save="search" />
    <!-- <Confirm :id="editId" v-model:open="open.confirm" @save="search" /> -->
  </PageContainer>
</template>
