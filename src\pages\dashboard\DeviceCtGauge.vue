<script setup lang="ts">
import { useDark } from '@vueuse/core'
import { computed } from 'vue'
import { responsivePx } from '~/utils/responsive'

const props = defineProps<{
  // 实际值
  actual: number
  // 理论值
  theoretical: number
  // 设备信息
  deviceInfo: {
    type: string
    code: string
    trackName: string
  }
  // 瓶颈
  bottleneck: boolean
}>()

const isDark = useDark()

const chartOption = computed(() => {
  const textColor = isDark.value ? '#E5EAF3' : '#666'

  return {
    tooltip: {
      formatter: '{b} : {c}s',
    },
    title: [
      {
        text: props.deviceInfo.type,
        left: 'center',
        top: '15%',
        textStyle: {
          fontSize: responsivePx(12),
          color: props.bottleneck ? '#F56C6C' : '#67C23A',
        },
      },
      {
        text: `${props.deviceInfo.code}`,
        left: 'center',
        top: '0%',
        textStyle: {
          fontSize: responsivePx(12),
          color: textColor,
        },
      },
    ],
    series: [{
      type: 'gauge',
      center: ['50%', '70%'],
      // radius: '70%',
      startAngle: 200,
      endAngle: -20,
      min: 0,
      max: Math.max(props.actual, props.theoretical) * 1.2,
      itemStyle: {
        color: props.bottleneck ? '#F56C6C' : '#67C23A',
      },
      progress: {
        show: true,
        roundCap: true,
        width: responsivePx(8),
      },
      pointer: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          width: responsivePx(8),
        },
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      anchor: {
        show: false,
      },
      detail: {
        valueAnimation: true,
        fontSize: responsivePx(12),
        offsetCenter: [0, '20%'],
        formatter: (value: number) => value === 0 ? '/' : `${value}s`,
        color: 'inherit',
      },
      data: [{
        value: props.actual,
      }],
    }],
  }
})
</script>

<template>
  <div class="h-full w-full flex items-center justify-center">
    <div class="h-full w-full">
      <e-charts :option="chartOption" autoresize />
    </div>
  </div>
</template>
