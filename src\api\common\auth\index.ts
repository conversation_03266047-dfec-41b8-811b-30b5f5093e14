import type { User } from '~/stores/user'
import { kyGet, kyPost } from '~/utils/request'

export interface oauthLoginParam {
  code: string
  state: string
}

export interface LoginParam {
  username: string
  password: string
}

export interface LoginRes {
  accessToken: string
  refreshToken: string
  user: User
}
export const authApi = {
  loginByCasdoor: (data: oauthLoginParam) => kyPost('auth/login/casdoor', undefined, data).json<LoginRes>(),
  login: (data: LoginParam) => kyPost('auth/login', undefined, data).json<LoginRes>(),
  getUserInfo: () => kyGet('auth/userInfo').json<User>(),
  refresh: (token: string) => kyPost('auth/refresh', undefined, { refreshToken: token }).text(),
}
