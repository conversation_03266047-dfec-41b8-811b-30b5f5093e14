import type { Reporter, Responder } from '../types'

// 对应 AnomaliesClassification 类
interface AnomaliesClassification {
  lineCode: string
  anomaliesCode: string
  anomaliesName?: string
  enable: boolean
}

// 对应 ResponseConfig 类
export interface ResponseConfig {
  pointTime: number
  responders: Responder[]
  reporters: Reporter[]
}

export type AnomaliesClassificationSearchParam = AnomaliesClassification

export interface AnomaliesClassificationCreateParam extends AnomaliesClassification {
  responseConfig: ResponseConfig[]
}

export interface AnomaliesClassificationUpdateParam extends AnomaliesClassification {
  responseConfig: ResponseConfig[]
}

export interface AnomaliesClassificationWithId extends AnomaliesClassification {
  id: string
  responseConfig: ResponseConfig[]
}
