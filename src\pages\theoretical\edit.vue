<script setup lang="ts">
import { useTheoreticalEditForm } from './schema'
import type { TheoreticalOutput } from '~/api/theoretical/types'
import { theoreticalApi } from '~/api/theoretical'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit, setValues } = useTheoreticalEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await theoreticalApi.update(props.id, values)
      success('创建成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const theoreticalOutput: TheoreticalOutput = await theoreticalApi.get(props.id)
    setValues(theoreticalOutput)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建理论值" @show="loadData">
    <form @submit="save">
      <FormLayout>
        <LInput name="lineCode" label="线体编码" />
        <LInput name="deviceCode" label="设备编码" />
        <LInput name="deviceType" label="设备类型" />
        <LInput name="productModel" label="产品型号" />
        <LInputNumber name="ct" label="理论CT" />
        <LInputNumber name="flatNumber" label="拼板数" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
