import { toTypedSchema } from '@vee-validate/zod'

const EventSearchSchema = toTypedSchema(z.object({
  mainCode: z.string().optional(),
  subCode: z.string().optional(),
  eventType: z.string().optional(),
}))

const EventCreateSchema = toTypedSchema(z.object({
  mainCode: z.string().optional(),
  subCode: z.string().optional(),
  eventType: z.string().optional(),
}))

const EventEditSchema = toTypedSchema(z.object({
  mainCode: z.string(),
  subCode: z.string(),
  mainSubject: z.string(),
  subSubject: z.string(),
  eventType: z.string().optional(),
}))

export function useEventSearchForm() {
  const form = useForm({
    validationSchema: EventSearchSchema,
  })
  return form
}

export function useEventCreateForm() {
  const form = useForm({
    validationSchema: EventCreateSchema,
  })
  return form
}

export function useEventEditForm() {
  const form = useForm({
    validationSchema: EventEditSchema,
  })
  return form
}
