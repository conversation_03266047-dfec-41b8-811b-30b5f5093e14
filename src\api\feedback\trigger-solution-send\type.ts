import type { NoticeUser } from '../trigger/type'

/** 快返触发解决方案发送表 */
export interface FeedbackTriggerSolutionSend {
  id: string // 主键ID
  expectedSendTime: Date // 预期发送时间
  sendTime?: Date // 发送时间
  sendUserId: NoticeUser[] // 发送人
  reportUserId: string[] // 告知人
  sendResult?: string // 发送结果
  triggerSolutionId: string // 解决方案ID
  triggerRecordId: string // 触发记录ID
  triggerSendId: string // 发送记录ID
  sendInfo?: string // 发送信息
  sendStatus: boolean // 是否发送
  messageType: string // 消息类型：SOLUTION_NOTIFICATION(解决方案通知), REJECT_NOTIFICATION(退回通知)
  createTime: Date // 创建时间
  updateTime: Date // 更新时间
  createBy: string // 创建人
  updateBy: string // 更新人
}

export interface FeedbackTriggerSolutionSendCreateParam {
  expectedSendTime: Date // 预期发送时间
  sendUserId: string[] // 发送人ID列表
  reportUserId: string[] // 告知人ID列表
  triggerSolutionId: string // 解决方案ID
  triggerRecordId: string // 触发记录ID
  triggerSendId: string // 发送记录ID
  sendInfo?: string // 发送信息
  sendStatus: boolean // 是否发送
  messageType: string // 消息类型
}

export interface FeedbackTriggerSolutionSendUpdateParam extends FeedbackTriggerSolutionSendCreateParam {
  sendTime?: Date // 发送时间
  sendResult?: string // 发送结果
}

export interface FeedbackTriggerSolutionSendSearchParam {
  triggerSolutionId?: string // 解决方案ID
  triggerRecordId?: string // 触发记录ID
  triggerSendId?: string // 发送记录ID
  sendStatus?: boolean // 发送状态
  messageType?: string // 消息类型
  expectedSendStartTime?: Date // 预期发送开始时间
  expectedSendEndTime?: Date // 预期发送结束时间
  sendStartTime?: Date // 发送开始时间
  sendEndTime?: Date // 发送结束时间
}

export interface FeedbackTriggerSolutionSendWithId extends FeedbackTriggerSolutionSend {
  id: string // 主键ID
}
