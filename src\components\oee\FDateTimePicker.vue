<script setup lang="ts">
interface Props {
  modelValue?: Date
  label?: string
  name?: string
  hourFormat?: '12' | '24'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  hourFormat: '24',
  disabled: false,
})

const emit = defineEmits<{
  updateFields: [value: Date]
}>()

const date = ref<Date>(props.modelValue || new Date())
const hours = ref(date.value.getHours().toString().padStart(2, '0'))
const minutes = ref(date.value.getMinutes().toString().padStart(2, '0'))
const seconds = ref(date.value.getSeconds().toString().padStart(2, '0'))

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    date.value = newValue
    updateTimeFields(newValue)
  }
})

function updateTimeFields(dateValue: Date) {
  hours.value = dateValue.getHours().toString().padStart(2, '0')
  minutes.value = dateValue.getMinutes().toString().padStart(2, '0')
  seconds.value = dateValue.getSeconds().toString().padStart(2, '0')
}

function updateDateTime() {
  const newDate = new Date(date.value)
  newDate.setHours(Number.parseInt(hours.value))
  newDate.setMinutes(Number.parseInt(minutes.value))
  newDate.setSeconds(Number.parseInt(seconds.value))
  emit('updateFields', newDate)
}

function validateTimeInput(value: string, max: number): string {
  const num = Number.parseInt(value)
  if (Number.isNaN(num))
    return '00'
  if (num < 0)
    return '00'
  if (num > max)
    return max.toString().padStart(2, '0')
  return num.toString().padStart(2, '0')
}

function onHoursInput(event: Event) {
  const input = event.target as HTMLInputElement
  hours.value = validateTimeInput(input.value, props.hourFormat === '24' ? 23 : 12)
  updateDateTime()
}

function onMinutesInput(event: Event) {
  const input = event.target as HTMLInputElement
  minutes.value = validateTimeInput(input.value, 59)
  updateDateTime()
}

function onSecondsInput(event: Event) {
  const input = event.target as HTMLInputElement
  seconds.value = validateTimeInput(input.value, 59)
  updateDateTime()
}
</script>

<template>
  <div class="flex flex-col gap-1 pt-6">
    <div class="flex items-center gap-2">
      <DatePicker
        v-model="date"
        :name="name"
        fluid
        date-format="yy-mm-dd"
        :disabled="disabled"
        @update:model-value="updateDateTime"
      />
      <div class="flex items-center gap-1">
        <input
          v-model="hours"
          type="text"
          class="w-12 border rounded p-2 text-center"
          :disabled="disabled"
          @input="onHoursInput"
        >
        <span>:</span>
        <input
          v-model="minutes"
          type="text"
          class="w-12 border rounded p-2 text-center"
          :disabled="disabled"
          @input="onMinutesInput"
        >
        <span>:</span>
        <input
          v-model="seconds"
          type="text"
          class="w-12 border rounded p-2 text-center"
          :disabled="disabled"
          @input="onSecondsInput"
        >
      </div>
    </div>
  </div>
</template>
