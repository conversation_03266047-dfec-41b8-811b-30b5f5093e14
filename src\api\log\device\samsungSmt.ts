export interface SamsungSmtLog {
  filename: string
  logtime: Date
  productname: string
  starttime: Date
  endtime: Date
  samsungindex: Index
  samsunglogheader: Samsunglogheader[]
  tapefeeder: Tapefeeder[]
  stickerfeeder: Stickerfeeder[]
  trayfeeder: Trayfeeder[]
  nozzle: Nozzle[]
  additionalinfo: Additionalinfo
}

interface Index {
  version: number // 版本号
  powertime: number // 开机时间（单位：秒）
  placetime: number // 放置时间
  waittime: number // 等待时间
  runtime: number // 运行时间 = 放置时间 + 等待时间 + 转移时间
  stoptime: number // 停止时间
  idletime: number // 空闲时间
  inwaittime: number // 输入等待时间
  outwaittime: number // 输出等待时间
  transtime: number // 转移时间
  wrongstoptime: number // 错误停止时间
  errorstoptime: number // 错误时间停止
  wrongstopcount: number // 错误停止计数
  errorstopcount: number // 错误停止计数
  panelincount: number // 面板输入计数
  paneloutcount: number // 面板输出计数
  panelcount: number // 面板计数
  pcbcount: number // PCB数量
  errorpcbcount: number // 错误PCB数量
  skippcbcount: number // 跳过的PCB数量
  operationrate: number // 运行率
  placementrate: number // 放置率
  meantimeperpcb: number // 单个PCB的平均时间
  realtimeperpcb: number // 实际每个PCB的时间
  transfertimeperpcb: number // 每个PCB的转移时间
  placecount: number // 穿过的数量
}

interface Samsunglogheader {
  head1: number // 贴片头编号
  head2: number // 备注
  head3: number // 拾取次数
  head4: number // 错误次数
  head5: number // 成功次数
  head6: number // 跳过次数
  head7: number // 漏拾次数
  head8: number // 错误拾取次数
}

interface Tapefeeder {
  position: string // 位置编号
  id: string // 送料器ID
  materialnum: string // 料号
  pickcount: number // 拾取数量
  errorcount: number // 错误数量
  successcount: number // 成功数量
  skipcount: number // 跳过数量
  misscount: number // 漏拾数量
  errorpickcount: number // 错误拾取数量
  location: string // 位置信息
}

interface Stickerfeeder {
  id: string // 送料器ID
  materialnum: string // 料号
  pickcount: number // 拾取数量
  errorcount: number // 错误数量
  successcount: number // 成功数量
  skipcount: number // 跳过数量
  misscount: number // 漏拾数量
  errorpickcount: number // 错误拾取数量
  location: string // 位置信息
}

interface Trayfeeder {
  id: string // 送料器ID
  materialnum: string // 料号
  pickcount: number // 拾取数量
  errorcount: number // 错误数量
  successcount: number // 成功数量
  skipcount: number // 跳过数量
  misscount: number // 漏拾数量
  errorpickcount: number // 错误拾取数量
  location: string // 位置信息
}

interface Nozzle {
  nozzlenum: number // 喷嘴编号
  nozzlechanger: string // 喷嘴型号
  pickcount: number // 拾取数量
  errorcount: number // 错误数量
  successcount: number // 成功数量
  skipcount: number // 跳过数量
  misscount: number // 漏拾数量
  errorpickcount: number // 错误拾取数量
  location: string // 位置信息
}

interface Additionalinfo {
  minimaltime: number // 最小周期时间
  fullbuildpcbcount: number // 完整生产的PCB数量
}
