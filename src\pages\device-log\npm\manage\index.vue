<script setup lang="ts">
import { productApi } from '~/api/product/manage'
import type { Product } from '~/api/product/manage/type'
import BaseLayer from '~/assets/images/base_layer.png'
import Npm from '~/assets/images/npm.png'
import { router } from '~/router'

const products = ref<Product[]>([])

function getProducts() {
  productApi.list().then((res) => {
    products.value = res
  })
}

function onCodeClick(index: number) {
  router.push(`/admin/product/info?id=${index}`)
}

getProducts()
</script>

<template>
  <div class="dashboard-container">
    <!-- Header Title -->
    <div class="header">
      LINE 304 NPM
    </div>

    <!-- Left Chart -->
    <div class="chart-box box1">
      <div class="chart chart1" />
    </div>

    <!-- Middle Chart -->
    <div class="box2">
      <div class="image-container">
        <!-- Use the imported Npm image variable for src -->
        <img :src="Npm" alt="Npm Image" class="image1">
        <img :src="BaseLayer" alt="BaseLayer Image" class="image2">
      </div>
    </div>

    <!-- Right Chart -->
    <div class="chart-box box3">
      <div id="chart3" class="chart" />
    </div>

    <!-- Bottom Chart -->
    <div class="chart-box box4">
      <div class="custom-table">
        <div class="table-header">
          <div class="header-item">
            时间
          </div>
          <div class="header-item">
            轨道
          </div>
          <div class="header-item">
            MJS 文件名
          </div>
          <div class="header-item">
            生产信息
          </div>
        </div>
        <div class="table-body">
          <div v-for="(product, index) in products" :key="product.filename" class="table-row">
            <div class="table-item">
              {{ product.date }}
            </div>
            <div class="table-item">
              {{ product.lane }}
            </div>
            <div class="table-item">
              {{ product.filename }}
            </div>
            <div class="table-item">
              <a href="#" @click.prevent="onCodeClick(index)">
                {{ product.productInfo }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="chart-box box5">
      <div id="chart5" class="chart" />
    </div>
  </div>
</template>

<style scoped>
dashboard-container {
  height: 100%;
  width: 100%;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  grid-template-rows: 80px 1.5fr 1fr;
  gap: 20px;
  background-image: url('~/assets/images/background_panel.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  color: #fff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
}

.header {
  grid-column: span 10;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition:
    transform 0.3s,
    box-shadow 0.3s;
}

.header:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

.chart-box {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transition:
    transform 0.3s,
    box-shadow 0.3s;
}

.chart-box:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.5);
}

.chart {
  width: 100%;
  height: 100%;
}

.box1 {
  grid-column-start: 1;
  grid-column-end: 3;
  padding: 0;
}

.box2 {
  grid-column-start: 3;
  grid-column-end: 7;
}

.box3 {
  grid-column-start: 7;
  grid-column-end: 11;
}

.box4 {
  grid-column-start: 1;
  grid-column-end: 7;
}

.box5 {
  grid-column-start: 7;
  grid-column-end: 11;
}

.chart1 {
  background: url('~/assets/images/rect.png') no-repeat center center / contain;
  background-size: 102% 102%;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  /* Use imported MarkLine for background */
  background-image: url('~/assets/images/mark_line.png');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.image1 {
  position: absolute;
  top: 20%;
  left: 0;
  width: 100%;
  height: 70%;
  object-fit: contain;
  z-index: 9999;
}

.image2 {
  position: absolute;
  top: 80%;
  left: 0;
  width: 100%;
  height: 20%;
  object-fit: contain;
}

.custom-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  color: #fff; /* Ensure text is readable */
}

/* Header styling */
.table-header {
  display: flex;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 3px 0;
}

.header-item {
  flex: 1;
  padding: 2px;
  text-align: center;
  font-weight: bold;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

/* Body row styling */
.table-row {
  display: flex;
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0.3s;
}

.table-row:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.05); /* Alternating row colors */
}

.table-row:hover {
  background-color: rgba(255, 255, 255, 0.2); /* Hover effect */
}

/* Item styling */
.table-item {
  flex: 1;
  padding: 2px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Link styling */
.table-item a {
  color: #ffecb3;
  text-decoration: none;
  transition: color 0.3s;
}

.table-item a:hover {
  color: #ffab40;
}
</style>
