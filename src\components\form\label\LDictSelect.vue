<script setup lang="ts">
import type { SelectProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
  code: string
  selectProps?: SelectProps
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <LabelFormItem :label="label" :name="name">
    <DictSelect v-model="value" :input-id="props.name" :code="code" option-label="label" option-value="value" :invalid="errorMessage ? true : false" fluid v-bind="selectProps" />
  </LabelFormItem>
</template>
