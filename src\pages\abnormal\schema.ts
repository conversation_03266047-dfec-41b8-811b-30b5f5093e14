import { toTypedSchema } from '@vee-validate/zod'

const AbnormalSearchSchema = toTypedSchema(z.object({
  classification: z.string().optional(),
  cause: z.string().optional(),
  responsible: z.string().optional(),
  department: z.string().optional(),
  writer: z.string().optional(),
  confirm: z.string().optional(),
  writeStartTime: z.date().optional(),
  writeEndTime: z.date().optional(),
  confirmStartTime: z.date().optional(),
  confirmEndTime: z.date().optional(),
}))

// 创建异常
const AbnormalCreateSchema = toTypedSchema(z.object({
  lineCode: z.string({
    required_error: '请选择线体',
  }).min(1),
  classification: z.string({
    required_error: '请选择分类',
  }),
  cause: z.string({
    required_error: '请输入原因',
  }).min(1),
  startTime: z.date({
    required_error: '请输入开始时间',
  }),
  endTime: z.date({
    required_error: '请输入结束时间',
  }),
  responsible: z.string({
    required_error: '请输入责任人',
  }).min(1),
  department: z.string({
    required_error: '请输入部门',
  }).min(1),
  writer: z.string({
    required_error: '请输入填写人',
  }).min(1),
  solution: z.string().optional(),
}))

// 更新异常
const AbnormalEditSchema = toTypedSchema(z.object({
  lineCode: z.string({
    required_error: '请选择线体',
  }).min(1),
  classification: z.string({
    required_error: '请选择分类',
  }),
  cause: z.string({
    required_error: '请输入原因',
  }).min(1),
  startTime: z.date({
    required_error: '请输入开始时间',
  }),
  endTime: z.date({
    required_error: '请输入结束时间',
  }),
  responsible: z.string({
    required_error: '请输入责任人',
  }).min(1),
  department: z.string({
    required_error: '请输入部门',
  }).min(1),
  writer: z.string({
    required_error: '请输入填写人',
  }).min(1),
  solution: z.string({
    required_error: '请输入解决方案',
  }).min(0).max(4000).optional(),
}))

const AbnormalConfirmSchema = toTypedSchema(
  z.object({
    confirm: z.string({
      required_error: '请输入确认人',
    }),
  }),
)

export function useAbnormalSearchForm() {
  const form = useForm({
    validationSchema: AbnormalSearchSchema,
  })
  return form
}

export function useAbnormalCreateForm() {
  const form = useForm({
    validationSchema: AbnormalCreateSchema,
  })
  return form
}

export function useAbnormalEditForm() {
  const form = useForm({
    validationSchema: AbnormalEditSchema,
  })
  return form
}

export function useAbnormalConfirmForm() {
  const form = useForm({
    validationSchema: AbnormalConfirmSchema,
  })
  return form
}
