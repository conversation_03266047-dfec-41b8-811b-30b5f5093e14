<script setup lang="ts">
import { storeToRefs } from 'pinia'
import AppMenuItem from './AppMenuItem.vue'
import { useMenuStore } from '~/stores/menu'

const { menu } = storeToRefs(useMenuStore())
</script>

<template>
  <ul class="layout-menu">
    <template v-for="(item, i) in menu" :key="item">
      <AppMenuItem v-if="!item.separator" :item="item" :index="i" :root="item.root" />
      <li v-if="item.separator" class="menu-separator" />
    </template>
  </ul>
</template>
