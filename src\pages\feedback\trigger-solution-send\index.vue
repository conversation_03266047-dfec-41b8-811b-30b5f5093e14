<script setup lang="ts">
import { formatDate } from '@vueuse/core'
import { useConfirm } from 'primevue'
import type { DataTablePageEvent } from 'primevue/datatable'
import Create from './Create.vue'
import Edit from './Edit.vue'
import { useFeedbackTriggerSolutionSendSearchForm } from './schema'
import { TriggerSolutionSendApi } from '~/api/feedback/trigger-solution-send'
import type { FeedbackTriggerSolutionSendWithId } from '~/api/feedback/trigger-solution-send/type'
import { error, success } from '~/composables/toast'

// 页面状态
const loading = ref(false)
const data = ref<FeedbackTriggerSolutionSendWithId[]>([])
const total = ref(0)
const selectedItems = ref<FeedbackTriggerSolutionSendWithId[]>([])

// 分页数据
const pageData = reactive({
  pageNumber: 0,
  pageSize: 10,
})

// 对话框状态
const createOpen = ref(false)
const editOpen = ref(false)
const editId = ref<string>()

// 消息类型选项
const messageTypeOptions = [
  { label: '全部', value: undefined },
  { label: '解决方案通知', value: 'SOLUTION_NOTIFICATION' },
  { label: '退回通知', value: 'REJECT_NOTIFICATION' },
]

// 搜索表单
const { handleSubmit: handleSearch, resetForm: resetSearchForm } = useFeedbackTriggerSolutionSendSearchForm()

// 确认对话框
const confirm = useConfirm()

// 获取数据
const fetchData = handleSearch(async (searchParams) => {
  try {
    loading.value = true
    const res = await TriggerSolutionSendApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  catch (err) {
    console.error('获取数据失败:', err)
    error('获取数据失败')
  }
  finally {
    loading.value = false
  }
})

// 分页处理
function onPage(event: DataTablePageEvent) {
  pageData.pageNumber = event.page || 0
  pageData.pageSize = event.rows || 10
  fetchData()
}

// 打开创建对话框
function openCreate() {
  createOpen.value = true
}

// 打开编辑对话框
function openEdit(id: string) {
  editId.value = id
  editOpen.value = true
}

// 删除单个记录
function deleteItem(id: string) {
  confirm.require({
    message: '确定要删除这条记录吗？',
    header: '删除确认',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '删除',
      severity: 'danger',
    },
    accept: async () => {
      try {
        await TriggerSolutionSendApi.delete(id)
        success('删除成功')
        fetchData()
      }
      catch (err) {
        console.error('删除失败:', err)
        error('删除失败')
      }
    },
  })
}

// 批量删除
function batchDelete() {
  if (selectedItems.value.length === 0) {
    error('请选择要删除的记录')
    return
  }

  confirm.require({
    message: `确定要删除选中的 ${selectedItems.value.length} 条记录吗？`,
    header: '批量删除确认',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '删除',
      severity: 'danger',
    },
    accept: async () => {
      try {
        const ids = selectedItems.value.map(item => item.id)
        await TriggerSolutionSendApi.batchDelete(ids)
        success('批量删除成功')
        selectedItems.value = []
        fetchData()
      }
      catch (err) {
        console.error('批量删除失败:', err)
        error('批量删除失败')
      }
    },
  })
}

// 手动发送
function sendItem(id: string) {
  confirm.require({
    message: '确定要手动发送这条记录吗？',
    header: '发送确认',
    icon: 'pi pi-question-circle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '发送',
      severity: 'success',
    },
    accept: async () => {
      try {
        await TriggerSolutionSendApi.send(id)
        success('发送成功')
        fetchData()
      }
      catch (err) {
        console.error('发送失败:', err)
        error('发送失败')
      }
    },
  })
}

// 批量发送
function batchSend() {
  if (selectedItems.value.length === 0) {
    error('请选择要发送的记录')
    return
  }

  confirm.require({
    message: `确定要发送选中的 ${selectedItems.value.length} 条记录吗？`,
    header: '批量发送确认',
    icon: 'pi pi-question-circle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: '发送',
      severity: 'success',
    },
    accept: async () => {
      try {
        const ids = selectedItems.value.map(item => item.id)
        await TriggerSolutionSendApi.batchSend(ids)
        success('批量发送成功')
        selectedItems.value = []
        fetchData()
      }
      catch (err) {
        console.error('批量发送失败:', err)
        error('批量发送失败')
      }
    },
  })
}

// 重置搜索
function resetSearch() {
  resetSearchForm()
  fetchData()
}

// 保存后刷新数据
function handleSave() {
  fetchData()
}

// 格式化消息类型
function formatMessageType(type: string) {
  const typeMap: Record<string, string> = {
    SOLUTION_NOTIFICATION: '解决方案通知',
    REJECT_NOTIFICATION: '退回通知',
  }
  return typeMap[type] || type
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<template>
  <PageContainer>
    <!-- 搜索表单 -->
    <form class="mb-6 px-4 space-y-4" @submit="fetchData">
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-4 md:grid-cols-2">
        <FInput name="triggerSolutionId" label="解决方案ID" />
        <FInput name="triggerRecordId" label="触发记录ID" />
        <FInput name="triggerSendId" label="发送记录ID" />
        <FSelect name="messageType" label="消息类型" :options="messageTypeOptions" />
      </div>
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-4 md:grid-cols-2">
        <FSelect
          name="sendStatus" label="发送状态" :options="[
            { label: '全部', value: undefined },
            { label: '已发送', value: true },
            { label: '未发送', value: false },
          ]"
        />
        <FDatePicker name="expectedSendStartTime" label="预期发送开始时间" :date-props="{ showTime: true }" />
        <FDatePicker name="expectedSendEndTime" label="预期发送结束时间" :date-props="{ showTime: true }" />
      </div>
      <div class="flex justify-end gap-2">
        <Button label="查询" icon="pi pi-search" type="submit" :loading="loading" />
        <Button label="重置" icon="pi pi-refresh" severity="secondary" type="button" @click="resetSearch" />
      </div>
    </form>

    <!-- 操作按钮 -->
    <div class="mb-4 flex justify-between">
      <div class="flex gap-2">
        <Button label="新增" icon="pi pi-plus" @click="openCreate" />
        <Button
          label="批量删除"
          icon="pi pi-trash"
          severity="danger"
          :disabled="selectedItems.length === 0"
          @click="batchDelete"
        />
        <Button
          label="批量发送"
          icon="pi pi-send"
          severity="success"
          :disabled="selectedItems.length === 0"
          @click="batchSend"
        />
      </div>
    </div>

    <!-- 数据表格 -->
    <DataTable
      v-model:selection="selectedItems"
      :value="data"
      :loading="loading"
      :total-records="total"
      :rows="pageData.pageSize"
      :first="pageData.pageNumber * pageData.pageSize"
      lazy
      paginator
      data-key="id"
      selection-mode="multiple"
      :meta-key-selection="false"
      @page="onPage"
    >
      <Column selection-mode="multiple" header-style="width: 3rem" />

      <Column field="triggerSolutionId" header="解决方案ID" sortable>
        <template #body="slotProps">
          <span class="text-sm font-mono">{{ slotProps.data.triggerSolutionId }}</span>
        </template>
      </Column>

      <Column field="triggerRecordId" header="触发记录ID" sortable>
        <template #body="slotProps">
          <span class="text-sm font-mono">{{ slotProps.data.triggerRecordId }}</span>
        </template>
      </Column>

      <Column field="messageType" header="消息类型" sortable>
        <template #body="slotProps">
          <Tag
            :value="formatMessageType(slotProps.data.messageType)"
            :severity=" slotProps.data.messageType === 'SOLUTION_NOTIFICATION' ? 'info' : 'warning'"
          />
        </template>
      </Column>

      <Column field="expectedSendTime" header="预期发送时间" sortable>
        <template #body="slotProps">
          {{ formatDate(new Date(slotProps.data.expectedSendTime), 'YYYY-MM-DD HH:mm:ss') }}
        </template>
      </Column>

      <Column field="sendTime" header="发送时间" sortable>
        <template #body="slotProps">
          {{ slotProps.data.sendTime ? formatDate(new Date(slotProps.data.sendTime), 'YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
      </Column>

      <Column field="sendStatus" header="发送状态" sortable>
        <template #body="slotProps">
          <Tag
            :value=" slotProps.data.sendStatus ? '已发送' : '未发送'"
            :severity=" slotProps.data.sendStatus ? 'success' : 'secondary'"
          />
        </template>
      </Column>

      <Column field="sendUserId" header="发送人数">
        <template #body="slotProps">
          {{ slotProps.data.sendUserId?.length || 0 }} 人
        </template>
      </Column>

      <Column field="reportUserId" header="告知人数">
        <template #body="slotProps">
          {{ slotProps.data.reportUserId?.length || 0 }} 人
        </template>
      </Column>

      <Column header="操作" :style="{ width: '200px' }">
        <template #body="slotProps">
          <div class="flex gap-2">
            <Button
              icon="pi pi-pencil"
              size="small"
              outlined
              @click="openEdit(slotProps.data.id)"
            />
            <Button
              icon="pi pi-trash"
              size="small"
              outlined
              severity="danger"
              @click="deleteItem(slotProps.data.id)"
            />
            <Button
              v-if="!slotProps.data.sendStatus"
              icon="pi pi-send"
              size="small"
              outlined
              severity="success"
              @click="sendItem(slotProps.data.id)"
            />
          </div>
        </template>
      </Column>
    </DataTable>

    <!-- 创建对话框 -->
    <Create v-model:open="createOpen" @save="handleSave" />

    <!-- 编辑对话框 -->
    <Edit :id="editId" v-model:open="editOpen" @save="handleSave" />
  </PageContainer>
</template>
