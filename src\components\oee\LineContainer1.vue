<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAnalyticsSearchSchema } from '~/pages/dashboard1/schema'

const props = defineProps<{
  code: string
  startTime: Date
  endTime: Date
}>()

const router = useRouter()

// 状态管理
const form = useAnalyticsSearchSchema()
const loading = ref<boolean>(false)

// 数据状态
const oee = ref({
  changeoverNum: '2',
  changeoverTime: '0.5',
  planTime: '12',
  actualPlanTime: '12',
  runTime: '12',
  stopTime: '12',
  actualStopTime: '12',
  actualBoard: '2000',
  planBoard: '2200',
  goodBoard: '1980',
  availability: '70.00%',
  availabilityTarget: '75.00%',
  performance: '98.75%',
  performanceTarget: '99.56%',
  defectCount: '20',
  quality: '99.00%',
  qualityTarget: '99.87%',
  oee: '68.43%',
  oeeTarget: '74.57%',
})

function generateMockData(code: string) {
  const mockData = {
    'SMT1-1': {
      changeoverNum: '2',
      changeoverTime: '0.5',
      planTime: '12',
      actualPlanTime: '12',
      runTime: '12',
      stopTime: '3',
      actualStopTime: '12',
      actualBoard: '2000',
      planBoard: '2200',
      goodBoard: '1980',
      availability: '70.00%',
      availabilityTarget: '75.00%',
      performance: '98.75%',
      performanceTarget: '99.56%',
      defectCount: '20',
      quality: '99.00%',
      qualityTarget: '99.87%',
      oee: '68.43%',
      oeeTarget: '74.57%',
    },
    'SMT1-2': {
      changeoverNum: '3',
      changeoverTime: '0.75',
      planTime: '10',
      actualPlanTime: '10',
      runTime: '9',
      stopTime: '1',
      actualStopTime: '1',
      actualBoard: '1800',
      planBoard: '2000',
      goodBoard: '1750',
      availability: '80.00%',
      availabilityTarget: '85.00%',
      performance: '95.00%',
      performanceTarget: '98.00%',
      defectCount: '15',
      quality: '98.50%',
      qualityTarget: '99.00%',
      oee: '75.00%',
      oeeTarget: '80.00%',
    },
    'SMT1-3': {
      changeoverNum: '1',
      changeoverTime: '0.25',
      planTime: '8',
      actualPlanTime: '8',
      runTime: '7',
      stopTime: '0.75',
      actualStopTime: '0.5',
      actualBoard: '4790',
      planBoard: '4800',
      goodBoard: '4785',
      availability: '88.76%',
      availabilityTarget: '95.00%',
      performance: '94.67%',
      performanceTarget: '98.00%',
      defectCount: '5',
      quality: '99.90%',
      qualityTarget: '99.95%',
      oee: '83.95%',
      oeeTarget: '93.01%',
    },
    'SMT1-4': {
      changeoverNum: '0',
      changeoverTime: '0',
      planTime: '9',
      actualPlanTime: '12',
      runTime: '0',
      stopTime: '0',
      actualStopTime: '0.5',
      actualBoard: '0',
      planBoard: '0',
      goodBoard: '0',
      availability: '0.00%',
      availabilityTarget: '90.00%',
      performance: '0.00%',
      performanceTarget: '99.00%',
      defectCount: '0',
      quality: '0.00%',
      qualityTarget: '99.90%',
      oee: '0.00%',
      oeeTarget: '89.01%',
    },
    'SMT1-5': {
      changeoverNum: '2',
      changeoverTime: '0.25',
      planTime: '10',
      actualPlanTime: '12',
      runTime: '9',
      stopTime: '0.5',
      actualStopTime: '0.5',
      actualBoard: '1992',
      planBoard: '2000',
      goodBoard: '1990',
      availability: '86.77%',
      availabilityTarget: '90.00%',
      performance: '94.23%',
      performanceTarget: '99.45%',
      defectCount: '2',
      quality: '99.90%',
      qualityTarget: '99.99%',
      oee: '81.68%',
      oeeTarget: '89.50%',
    },
    'LS1-1': {
      changeoverNum: '2',
      changeoverTime: '0.5',
      planTime: '9',
      actualPlanTime: '10',
      runTime: '8',
      stopTime: '1',
      actualStopTime: '0.5',
      actualBoard: '1595',
      planBoard: '1600',
      goodBoard: '1592',
      availability: '76.65%',
      availabilityTarget: '87.00%',
      performance: '96.56%',
      performanceTarget: '99.45%',
      defectCount: '3',
      quality: '99.81%',
      qualityTarget: '99.90%',
      oee: '73.87%',
      oeeTarget: '86.43%',
    },
    'LS1-2': {
      changeoverNum: '3',
      changeoverTime: '0.25',
      planTime: '10',
      actualPlanTime: '12',
      runTime: '9',
      stopTime: '0.5',
      actualStopTime: '0.5',
      actualBoard: '2095',
      planBoard: '2100',
      goodBoard: '2094',
      availability: '85.23%',
      availabilityTarget: '90.00%',
      performance: '95.76%',
      performanceTarget: '99.45%',
      defectCount: '1',
      quality: '99.95%',
      qualityTarget: '99.99%',
      oee: '81.58%',
      oeeTarget: '89.50%',
    },
    'LS1-3': {
      changeoverNum: '1',
      changeoverTime: '0.25',
      planTime: '8',
      actualPlanTime: '8',
      runTime: '7',
      stopTime: '0.75',
      actualStopTime: '0.5',
      actualBoard: '2988',
      planBoard: '3000',
      goodBoard: '2982',
      availability: '88.76%',
      availabilityTarget: '95.00%',
      performance: '94.67%',
      performanceTarget: '98.00%',
      defectCount: '6',
      quality: '99.80%',
      qualityTarget: '99.90%',
      oee: '83.86%',
      oeeTarget: '93.01%',
    },
    'LS1-4': { changeoverNum: '2', changeoverTime: '0.5', planTime: '9', actualPlanTime: '12', runTime: '8', stopTime: '0.25', actualStopTime: '0.5', actualBoard: '1800', planBoard: '2000', goodBoard: '1785', availability: '85.00%', availabilityTarget: '90.00%', performance: '97.00%', performanceTarget: '99.00%', defectCount: '15', quality: '99.17%', qualityTarget: '99.90%', oee: '81.77%', oeeTarget: '89.01%' },
    'LS1-5': { changeoverNum: '0', changeoverTime: '0', planTime: '1', actualPlanTime: '2', runTime: '1.5', stopTime: '1', actualStopTime: '0.5', actualBoard: '1', planBoard: '1', goodBoard: '1', availability: '84.76%', availabilityTarget: '88.00%', performance: '100.00%', performanceTarget: '99.45%', defectCount: '0', quality: '100.00%', qualityTarget: '99.90%', oee: '84.76%', oeeTarget: '87.43%' },
  }

  return mockData[code as keyof typeof mockData] || mockData['SMT1-1']
}

const loadData = form.handleSubmit(async () => {
  loading.value = true
  try {
    oee.value = generateMockData(props.code)
  }
  finally {
    loading.value = false
  }
})

// 生命周期和监听器
watch(props, (newProps) => {
  if (newProps) {
    // 设置产品线
    form.setFieldValue('code', newProps.code)
    form.setFieldValue('startTime', newProps.startTime)
    form.setFieldValue('endTime', newProps.endTime)
    // 加载数据
    loadData()
  }
}, { immediate: true, deep: true })

// 点击线体跳转到具体产线数据
function navigateToLineDetail() {
  router.push({ name: 'dashboard1', params: { code: props.code } })
}
</script>

<template>
  <div class="w-full" :class="{ light: !isDark, dark: isDark }">
    <div class="grid grid-cols-[20%_80%] grid-rows-1 gap-1 rounded-lg from-gray-700 via-gray-900 to-black bg-gradient-to-r pt-2 shadow-lg">
      <!-- 点击线体跳转 -->
      <div
        class="row-span-2 flex flex-col transform cursor-pointer items-center justify-center transition-transform panel hover:scale-105"
        @click="navigateToLineDetail"
      >
        <span class="text-2xl text-white">线体</span>
        <span class="text-3xl text-blue-500 font-bold">{{ props.code }}</span>
      </div>
      <div class="grid grid-cols-12 gap-1">
        <div class="col-span-2 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">开班时间</span>
          <span class="text-xl text-primary font-bold">{{ oee?.actualPlanTime }} h</span>
        </div>
        <div class="col-span-2 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">运机时间</span>
          <span class="text-xl text-primary font-bold">{{ oee?.runTime }} h</span>
        </div>
        <div class="col-span-2 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">停机时间</span>
          <span class="text-xl text-primary font-bold">{{ oee?.stopTime }} h</span>
        </div>
        <div class="col-span-3 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">标准生产数量 | 实际生产数量</span>
          <span class="text-xl text-primary font-bold">{{ oee?.planBoard }} | {{ oee?.actualBoard }}</span>
        </div>
        <div class="col-span-1 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">不良品数</span>
          <span class="text-xl text-primary font-bold">{{ oee?.defectCount || 0 }}</span>
        </div>
        <div class="col-span-2 w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">换线次数 | 换线时间</span>
          <span class="text-xl text-primary font-bold">{{ oee?.changeoverNum || 0 }} | {{ oee?.changeoverTime }}</span>
        </div>
      </div>

      <div class="grid grid-cols-4 gap-1">
        <div class="w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">目标运转率 | 实际运转率</span>
          <span class="text-xl text-primary font-bold">{{ oee?.availabilityTarget }}% | {{ oee?.availability }}%</span>
        </div>
        <div class="w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">目标有效生产率 | 实际有效生产率</span>
          <span class="text-xl text-primary font-bold">{{ oee?.performanceTarget }}% | {{ oee?.performance }}%</span>
        </div>
        <div class="w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">目标良品率 | 实际良品率</span>
          <span class="text-xl text-primary font-bold">{{ oee?.qualityTarget }}% | {{ oee?.quality }}%</span>
        </div>
        <div class="w-full flex flex-col transform items-center justify-center rounded-lg bg-gray-800 py-1 shadow-md transition-transform panel hover:scale-105">
          <span class="text-sm text-white">目标OEE | 实际OEE</span>
          <span class="text-xl text-primary font-bold">{{ oee?.oeeTarget }}% | {{ oee?.oee }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.panel {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px;
  transition: transform 0.3s ease;
}
.panel:hover {
  transform: scale(1.05);
}
</style>
