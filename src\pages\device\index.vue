<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import Create from './create.vue'
import Edit from './edit.vue'
import { useDeviceSearchForm } from './schema'
import type { PageData } from '~/api/common/type'
import { deviceApi } from '~/api/device'
import type { Device } from '~/api/device/types'

const loading = ref<boolean>(false)
const editId = ref<string>()
const open = reactive({
  create: false,
  edit: false,
})
const data = ref<Device[]>([])
const selectedItems = ref<Device[]>([])
const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})
const total = ref(0)
const searchForm = useDeviceSearchForm()

const search = searchForm.handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await deviceApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

// const confirm = useConfirm()
// function confirmDel(id: string, event: any) {
//   confirm.require({
//     target: event.currentTarget,
//     message: '确认删除？',
//     group: 'delete',
//     accept: async () => {
//       await deviceApi.delete(id)
//       success('删除成功')
//       data.value = data.value.filter(o => o.id !== id)
//     },
//   })
// }

// function confirmBatchDel(event: any) {
//   if (!selectedItems.value.length) {
//     warn('请选择要删除的项目')
//     return
//   }

//   confirm.require({
//     target: event.currentTarget,
//     message: `确认删除选中的 ${selectedItems.value.length} 项？`,
//     group: 'delete',
//     accept: async () => {
//       try {
//         loading.value = true
//         await deviceApi.batchDelete(selectedItems.value.map(item => item.id))
//         success('批量删除成功')
//         selectedItems.value = []
//         search()
//       }
//       finally {
//         loading.value = false
//       }
//     },
//   })
// }

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

// function openCreatePage() {
//   open.create = true
// }

// function openEditPage(id: string) {
//   open.edit = true
//   editId.value = id
// }

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer class="mt-4">
    <SearchBox :loading="loading" @submit="search" @search="search">
      <FInput name="lineId" label="线体编码" />
      <FInput name="name" label="设备名称" />
      <FInput name="code" label="设备编码" />
      <!-- <FSelect name="type" label="设备类型" :options="deviceTypeOptions" />
      <FSelect name="category" label="设备类别" :options="deviceCategoryOptions" /> -->
    </SearchBox>

    <!-- <ButtonGroup class="pl-8">
      <Button outlined icon="pi pi-plus" @click="openCreatePage()" />
      <Button
        outlined
        severity="danger"
        icon="pi pi-trash"
        :disabled="!selectedItems.length"
        @click="confirmBatchDel($event)"
      />
    </ButtonGroup> -->

    <DataTable
      v-model:selection="selectedItems"
      class="p-4"
      :value="data"
      lazy
      paginator
      data-key="id"
      :rows="pageData.pageSize"
      :total-records="total"
      @page="page"
    >
      <!-- <Column selection-mode="multiple" :frozen="true" style="width: 3rem" /> -->
      <Column field="lineId" header="线体编码" />
      <Column field="name" header="设备名称" />
      <Column field="code" header="设备编码" />
      <Column field="type" header="设备类型" />
      <Column field="category" header="设备类别" />
      <Column field="track" header="轨道" />
      <Column field="enable" header="状态">
        <template #body="slotProps">
          {{ slotProps.data.enable === 1 ? '启用' : '禁用' }}
        </template>
      </Column>
      <Column field="sort" header="序号" />
      <Column field="group" header="分组ID" />
      <!-- <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined icon="pi pi-pencil" @click="openEditPage(slotProps.data.id)" />
            <Button outlined severity="danger" icon="pi pi-trash" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column> -->
    </DataTable>

    <Create v-model:open="open.create" @save="search" />
    <Edit :id="editId" v-model:open="open.edit" @save="search" />
  </PageContainer>
</template>
