export interface Abnormal {
  id: string
  lineCode: string
  classification: string
  cause: string
  startTime: Date
  endTime: Date
  responsible: string
  department: string
  writer: string
  writeTime: Date
  confirm: string
  confirmTime: Date
  solution: string
}

// 异常查询参数
export interface AbnormalSearchParam {
  lineCode: string
  classification: string
  cause: string
  responsible: string
  department: string
  writer: string
  confirm: string
  writeStartTime: Date
  writeEndTime: Date
  confirmStartTime: Date
  confirmEndTime: Date
}

// 异常创建参数
export interface AbnormalCreateParam {
  lineCode: string
  classification: string
  cause: string
  startTime: Date
  endTime: Date
  responsible: string
  department: string
  writer: string
  solution?: string
}

// 异常更新参数
export interface AbnormalEditParam {
  lineCode: string
  classification: string
  cause: string
  startTime: Date
  endTime: Date
  responsible: string
  department: string
  writer: string
  solution?: string
}

export interface AbnormalConfirmParam {
  confirm: string
}
