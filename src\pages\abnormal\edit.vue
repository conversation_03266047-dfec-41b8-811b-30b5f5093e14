<script setup lang="ts">
import { useAbnormalEditForm } from './schema'
import type { Abnormal } from '~/api/abnormal/types'
import { abnormalApi } from '~/api/abnormal'
import LTextarea from '~/components/form/label/LTextarea.vue'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit, setValues } = useAbnormalEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await abnormalApi.update(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const abnormal: Abnormal = await abnormalApi.get(props.id)
    setValues(abnormal)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="异常编辑" @show="loadData">
    <form @submit="save">
      <FormVertical>
        <LDictSelect name="lineCode" label="线体编码" code="LINE_CODE" />
        <LDictSelect name="classification" label="异常分类" code="ABNORMAL_CODE" />
        <LDatePicker name="startTime" label="开始时间" :date-props="{ showTime: true, showSeconds: true }" />
        <LDatePicker name="endTime" label="结束时间" :date-props="{ showTime: true, showSeconds: true }" />
        <LInput name="cause" label="异常原因" />
        <LInput name="responsible" label="责任人" />
        <LTextarea class="grid-col-span-2" name="solution" label="解决方案" />
        <LInput name="department" label="责任部门" />
        <LInput name="writer" label="填写人" />
      </FormVertical>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
