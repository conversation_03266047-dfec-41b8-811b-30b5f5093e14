import type { RouteRecordRaw } from 'vue-router'

export default [
  {
    path: '/:code?',
    name: 'home',
    component: () => import('~/pages/index.vue'),
    meta: {
      isPublic: true,
      noLayout: true,
    },
    beforeEnter: async (to, from, next) => {
      if (to.params.code) {
        next()
      }
      else {
        const workshop = useWorkshop()
        await workshop.init()
        workshop.setActiveWorkShopItem(workshop.workshop.value[0]?.code)
        next({ name: 'home', params: { code: workshop.workshop.value[0]?.code } })
      }
    },
  },
  {
    path: '/fake/:code?',
    name: 'home1',
    component: () => import('~/pages/index1.vue'),
    meta: {
      isPublic: true,
      noLayout: true,
    },
    beforeEnter: async (to, from, next) => {
      if (to.params.code) {
        next()
      }
      else {
        const workshop = useWorkshop()
        await workshop.init()
        workshop.setActiveWorkShopItem(workshop.workshop.value[0]?.code)
        next({ name: 'home1', params: { code: workshop.workshop.value[0]?.code } })
      }
    },
  },
] as RouteRecordRaw[]
