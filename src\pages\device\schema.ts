import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { DeviceCategory, DeviceType } from '~/api/device/types'

const DeviceSearchSchema = toTypedSchema(z.object({
  lineId: z.string().optional(),
  name: z.string().optional(),
  code: z.string().optional(),
}))

const DeviceCreateSchema = toTypedSchema(z.object({
  name: z.string().min(1, '请输入设备名称'),
  code: z.string().min(1, '请输入设备编码'),
  type: z.nativeEnum(DeviceType, {
    required_error: '请选择设备类型',
  }),
  category: z.nativeEnum(DeviceCategory, {
    required_error: '请选择设备类别',
  }),
  track: z.string().min(1, '请输入轨道'),
  lineId: z.string().min(1, '请选择产线'),
  clientIp: z.string().min(1, '请输入客户端IP'),
  clientPort: z.string().min(1, '请输入客户端端口'),
  sort: z.number().int().min(0),
  group: z.number().int().min(0),
  enable: z.number().int().min(0).max(1),
}))

export function useDeviceSearchForm() {
  return useForm({
    validationSchema: DeviceSearchSchema,
  })
}

export function useDeviceCreateForm() {
  return useForm({
    validationSchema: DeviceCreateSchema,
  })
}

export function useDeviceEditForm() {
  return useForm({
    validationSchema: DeviceCreateSchema,
  })
}
