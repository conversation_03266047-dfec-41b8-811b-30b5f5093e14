export interface DictItem {
  label: string
  value: string
}

export interface DictSimple {
  code: string
  name: string
}

export interface DictCreateParam extends DictSimple {
  itemList: DictItem[]
}

export type DictUpdateParam = DictCreateParam

export type DictSearchParam = DictSimple

export interface DictSimpleWithId extends DictSimple {
  id: string
}

export interface Dict extends DictCreateParam {
  id: string
}
