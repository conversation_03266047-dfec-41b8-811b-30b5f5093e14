<script setup lang="ts">
import { type DataTablePageEvent, useConfirm } from 'primevue'

import { useUserQueryForm } from './schema'

import Create from './Create.vue'
import Edit from './Edit.vue'
import PageContainer from '~/components/common/PageContainer.vue'
import type { PageData } from '~/api/common/type'
import type { UserView } from '~/api/common/user/types'
import { userApi } from '~/api/common/user'

const open = ref({
  create: false,
  edit: false,
})
const editId = ref<string>()

const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

const total = ref(0)

const loading = ref<boolean>(false)

const data = ref<UserView[]>([])

const confirm = useConfirm()
const searchForm = useUserQueryForm()

const search = searchForm.handleSubmit(async (values) => {
  try {
    loading.value = true
    const res = await userApi.page({ searchParams: values, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

function openEditPage(id: string) {
  editId.value = id
  open.value.edit = true
}

function openCreatePage() {
  open.value.create = true
}

function confirmDel(id: string, event: any) {
  confirm.require({
    target: event.currentTarget,
    message: '确认删除？',
    acceptLabel: '是',
    rejectLabel: '否',
    rejectClass: 'outline',
    group: 'delete',
    accept: async () => {
      await userApi.del(id)
      success('删除成功')
      data.value = data.value.filter(o => o.id !== id)
    },
  })
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer class="mt-4">
    <SearchBox :loading="loading" @submit="search">
      <FInput name="name" label="用户名" />
      <FInput name="displayName" label="姓名" />
    </SearchBox>
    <div class="pl-4">
      <Button outlined @click="openCreatePage">
        新增
      </Button>
    </div>
    <DataTable class="mt-4 p-4" :value="data" lazy paginator data-key="id" :rows="pageData.pageSize" :total-records="total" @page="page">
      <Column field="id" header="ID" />
      <Column field="name" header="用户名" />
      <Column field="displayName" header="姓名" />

      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined @click="openEditPage(slotProps.data.id)">
              编辑
            </Button>
            <Button icon="pi pi-trash" outlined severity="danger" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column>
    </DataTable>
    <Create v-model:open="open.create" @save="search" />
    <!-- <DemoCreate v-model:open="open.create" @save="search" /> -->
    <Edit :id="editId" v-model:open="open.edit" @save="search" />
  </PageContainer>
</template>
