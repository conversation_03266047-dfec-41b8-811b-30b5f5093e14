<script setup lang="ts">
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  code: string
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <DictSelect v-model="value" :input-id="props.name" :code="code" option-label="label" option-value="value" :invalid="errorMessage ? true : false" fluid v-bind="$attrs" />
  <ErrorMsg :error-message="errorMessage" />
</template>
