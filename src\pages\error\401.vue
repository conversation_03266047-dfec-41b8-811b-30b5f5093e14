<script setup lang="ts">
import But<PERSON> from 'primevue/button'
import { useRouter } from 'vue-router'

const router = useRouter()

function toLoginPage() {
  router.push('/login')
}
</script>

<template>
  <div class="h-full flex flex-col items-center justify-center">
    <div class="text-lg">
      401 UNAUTHORIZED
    </div>
    <Button @click="toLoginPage">
      登录
    </Button>
  </div>
</template>
