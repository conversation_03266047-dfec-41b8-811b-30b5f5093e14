<script setup lang="ts">
import { useDark } from '@vueuse/core'
import { responsivePx } from '~/utils/responsive'
import type { DefectTypeResult } from '~/api/analyze/type'

const props = defineProps<{
  data: DefectTypeResult
}>()

const SERIES_COLORS = [
  { start: '#FF9800', end: '#FFB74D' },
  { start: '#4CAF50', end: '#81C784' },
  { start: '#2196F3', end: '#64B5F6' },
  { start: '#9C27B0', end: '#BA68C8' },
]

const isDark = useDark()

const dynamicChartStyles = computed(() => {
  const isDarkValue = isDark.value
  return {
    textColor: isDarkValue ? '#E5EAF3' : '#666',
    splitLineColor: isDarkValue ? 'rgba(84,89,104,0.3)' : '#DDD',
    axisLineColor: isDarkValue ? '#626675' : '#999',
  }
})

const baseChartOption = computed((): EChartsOption => {
  return {
    dataset: {
      dimensions: props.data.dimensions,
      source: props.data.data,
    },
    series: props.data.headMaps.map((item, index) => ({
      name: item.name,
      type: item.type as 'bar' | 'line', // 添加更精确的类型断言
      barWidth: '25%',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: SERIES_COLORS[index % SERIES_COLORS.length].start,
          }, {
            offset: 1,
            color: SERIES_COLORS[index % SERIES_COLORS.length].end,
          }],
        },
        borderRadius: [4, 4, 0, 0],
      },
    })),
  }
})

const chartOption = computed((): EChartsOption => {
  const styles = dynamicChartStyles.value

  return {
    ...baseChartOption.value, // 复用基础配置
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
    },
    legend: {
      data: props.data.headMaps.map(item => item.name),
      textStyle: {
        color: styles.textColor,
        fontSize: responsivePx(12),
      },
      itemWidth: responsivePx(12),
      itemHeight: responsivePx(6),
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '25%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        interval: 0,
        width: responsivePx(40),
        overflow: 'break',
        lineHeight: responsivePx(12),
        color: styles.textColor,
        fontSize: responsivePx(12),
      },
      axisLine: { lineStyle: { color: styles.axisLineColor } },
      axisTick: { show: false },
    },
    yAxis: {
      type: 'value',
      name: '数量',
      minInterval: 1,
      min: 0,
      splitLine: { lineStyle: { type: 'dashed', color: styles.splitLineColor } },
      nameTextStyle: { color: styles.textColor, fontSize: responsivePx(12) },
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        formatter: '{value}',
        color: styles.textColor,
        fontSize: responsivePx(12),
      },
    },
    label: {
      show: true,
      position: 'top',
      fontSize: responsivePx(12),
      color: styles.textColor,
    },
  }
})
</script>

<template>
  <e-charts class="chart" :option="chartOption" autoresize />
</template>
