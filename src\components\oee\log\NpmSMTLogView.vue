<script setup lang="ts">
import { formatDate } from '@vueuse/core'
import { HTTPError } from 'ky'
import { logApi } from '~/api/log'

import type { NpmSmtLog } from '~/api/log/device/npmSmt'

const props = defineProps<{
  deviceCode: string
  id: string
}>()

const data = ref<NpmSmtLog>()

watch(() => props, async () => {
  search()
})

async function search() {
  try {
    const res = await logApi.getProductionLog({
      deviceCode: props.deviceCode,
      id: props.id,
    })
    data.value = res
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(ex.message)
    }
  }
}

onMounted(() => {
  search()
})
</script>

<template>
  <div class="w-full p-4">
    <div class="mb-4 w-full flex justify-center text-2xl font-bold">
      NPM SMT LOG
    </div>
    <Panel header="基本信息" class="mb-4 w-full">
      <div class="grid grid-cols-8 gap-4 p-4">
        <div> 日志名称 </div><div>{{ data?.filename }}</div>
        <div>日志时间</div><div>{{ data?.logtime ? formatDate(data?.logtime, 'YYYY-MM-DD HH:mm:ss') : '' }}</div>
        <div>计数(板)</div><div>{{ data?.generalcount.board }}</div>
        <div>计数（模块）</div><div>{{ data?.generalcount.module }}</div>
        <div>总时间</div><div>{{ data?.generaltime.total }} ms</div>
        <div>运行时间</div><div>{{ data?.generaltime.actual }} ms</div>
      </div>
    </Panel>
    <Panel header="停机时间(ms)" class="mb-4 w-full">
      <div class="grid grid-cols-8 gap-4 p-4">
        <div>total</div><div>{{ data?.stoptime.total }}</div>
        <div>fwait</div><div>{{ data?.stoptime.fwait }}</div>
        <div>rwait</div><div>{{ data?.stoptime.rwait }}</div>
        <div>pwait</div><div>{{ data?.stoptime.pwait }}</div>
        <div>scstop</div><div>{{ data?.stoptime.scstop }}</div>
        <div>trbl</div><div>{{ data?.stoptime.trbl }}</div>
        <div>scestop</div><div>{{ data?.stoptime.scestop }}</div>
        <div>othrstop</div><div>{{ data?.stoptime.othrstop }}</div>
        <div>crerr</div><div>{{ data?.stoptime.crerr }}</div>
        <div>cterr</div><div>{{ data?.stoptime.cterr }}</div>
        <div>cnvstop</div><div>{{ data?.stoptime.cnvstop }}</div>
        <div>brcgstop</div><div>{{ data?.stoptime.brcgstop }}</div>
      </div>
    </Panel>
    <Panel header="停机次数" class="mb-4 w-full">
      <div class="grid grid-cols-8 gap-4 p-4">
        <div>total</div><div>{{ data?.stopcount.total }}</div>
        <div>fwait</div><div>{{ data?.stopcount.fwait }}</div>
        <div>rwait</div><div>{{ data?.stopcount.rwait }}</div>
        <div>pwait</div><div>{{ data?.stopcount.pwait }}</div>
        <div>scstop</div><div>{{ data?.stopcount.scstop }}</div>
        <div>trbl</div><div>{{ data?.stopcount.trbl }}</div>
        <div>scestop</div><div>{{ data?.stopcount.scestop }}</div>
        <div>othrstop</div><div>{{ data?.stopcount.othrstop }}</div>
        <div>crerr</div><div>{{ data?.stopcount.crerr }}</div>
        <div>cterr</div><div>{{ data?.stopcount.cterr }}</div>
        <div>cnvstop</div><div>{{ data?.stopcount.cnvstop }}</div>
        <div>brcgstop</div><div>{{ data?.stopcount.brcgstop }}</div>
      </div>
    </Panel>
    <Panel header="吸取数（供料器）" class="mb-4 w-full">
      <DataTable :value="data?.takeupunits" :rows="10" scrollable paginator scroll-height="800px">
        <Column field="head" header=" head" />
        <Column field="unit" header="unit" />
        <Column field="side" header="side" />
        <Column field="tcnt" header="tcnt" />
      </DataTable>
    </Panel>
    <Panel header="吸取数（吸嘴）" class="mb-4 w-full">
      <DataTable :value="data?.takeupnozzles" :rows="10" scrollable paginator scroll-height="800px">
        <Column field="head" header="head" />
        <Column field="nozzlestocker" header="nozzlestocker" />
        <Column field="nozzlechanger" header="nozzlechanger" />
        <Column field="tcnt" header="tcnt" />
      </DataTable>
    </Panel>
    <Panel header="miss数（供料器）" class="mb-4 w-full">
      <DataTable :value="data?.missunits" :rows="10" scrollable paginator scroll-height="800px">
        <Column field="head" header=" head" />
        <Column field="unit" header="unit" />
        <Column field="side" header="side" />
        <Column field="tmiss" header="tmiss" />
        <Column field="rmiss" header="rmiss" />
        <Column field="hmiss" header="hmiss" />
        <Column field="dmiss" header="dmiss" />
        <Column field="mmiss" header="mmiss" />
        <Column field="rsmiss" header="rsmiss" />
      </DataTable>
    </Panel>
    <Panel header="miss数（吸嘴）" class="mb-4 w-full">
      <DataTable :value="data?.missnozzles" :rows="10" scrollable paginator scroll-height="800px">
        <Column field="head" header=" head" />
        <Column field="nozzlestocker" header="nozzlestocker" />
        <Column field="nozzlechanger" header="nozzlechanger" />
        <Column field="tmiss" header="tmiss" />
        <Column field="rmiss" header="rmiss" />
        <Column field="hmiss" header="hmiss" />
        <Column field="dmiss" header="dmiss" />
        <Column field="mmiss" header="mmiss" />
        <Column field="rsmiss" header="rsmiss" />
      </DataTable>
    </Panel>
  </div>
</template>
