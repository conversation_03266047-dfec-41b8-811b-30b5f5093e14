<script setup lang="ts">
import DashboardMenuItem from './DashboardMenuItem.vue'

const { line } = useProductline()
</script>

<template>
  <div class="layout-menu">
    <ul>
      <li class="layout-root-menuitem">
        <div class="layout-menuitem-root-text">
          线体选择
        </div>
      </li>
      <template v-for="(item, i) in line" :key="item">
        <DashboardMenuItem :item="item" :index="i" />
      </template>
    </ul>
  </div>
</template>
