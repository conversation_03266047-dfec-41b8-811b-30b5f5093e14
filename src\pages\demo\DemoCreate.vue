<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { demoOptions, useDemoData, useDemoForm } from './schema'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit } = useDemoForm()
const { push, remove, fields } = useFieldArray('items')

const api = await useDemoData()

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await api.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建demo" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="名称" />
        <LInput name="description" label="描述" />
        <LDatePicker name="date1" label="日期1" />
        <LDatePicker name="date2" label="日期2" />
        <LSelect name="option1" label="选项1" :options="demoOptions" />
        <LSelect name="option2" label="选项2" :options="demoOptions" />
        <LRadioGroup name="radio" label="选项3" :options="demoOptions" />
        <LBoolRadioGroup name="yesno" label="是否" />
        <LDatePicker :date-props="{ timeOnly: true }" name="time" label="时间" />
      </FormLayout>
      <Button outlined class="mt-4" @click="push({})">
        新增
      </Button>
      <DataTable :value="fields" data-key="key">
        <Column header="名称">
          <template #body="slotProps">
            <SInput :name="`items[${slotProps.index}].label`" />
          </template>
        </Column>
        <Column header="选项1">
          <template #body="slotProps">
            <SSelect :name="`items[${slotProps.index}].option`" :options="demoOptions" />
          </template>
        </Column>
        <Column header="日期">
          <template #body="slotProps">
            <SDatePicker :name="`items[${slotProps.index}].date`" />
          </template>
        </Column>
        <Column header="操作">
          <template #body="slotProps">
            <Button severity="danger" @click="remove(slotProps.index)">
              删除
            </Button>
          </template>
        </Column>
      </DataTable>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
