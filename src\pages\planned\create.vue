<script setup lang="ts">
import { usePlannedCapacityCreateForm } from './schema'
import { capacityApi } from '~/api/capacity'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit } = usePlannedCapacityCreateForm()

const save = handleSubmit(async (formValues) => {
  try {
    loading.value = true
    await capacityApi.create(formValues)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建计划产能" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LDictSelect name="lineCode" label="线体编码" code="LINE_CODE" />
        <LInput name="productModel" label="产品型号" />
        <LInputNumber name="plannedQuantity" label="标准产能" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
