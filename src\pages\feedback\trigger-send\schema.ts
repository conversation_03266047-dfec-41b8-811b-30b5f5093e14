import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { z } from '~/utils/zext'
import type { FeedbackTriggerSendSearchParam } from '~/api/feedback/trigger-send/type'

const TriggerSendSearchSchema = toTypedSchema(
  z.object({
    triggerRecordId: z.string().optional(),
    sendStatus: z.enum(['UNSENT', 'SENT', 'STOPPED'] as const).optional(),
    expectedSendStartTime: z.date().optional(),
    expectedSendEndTime: z.date().optional(),
    sendStartTime: z.date().optional(),
    sendEndTime: z.date().optional(),
  }),
)

const TriggerSendCreateSchema = toTypedSchema(
  z.object({
    expectedSendTime: z.date({
      required_error: '请选择预期发送时间',
    }),
    sendUserId: z.array(z.string().min(1)).min(1, '至少选择一个响应人').default([]),
    reportUserId: z.array(z.string().min(1)).min(1, '至少选择一个告知人').default([]),
    triggerRecordId: z.string().min(1, '请输入触发记录ID'),
    sendInfo: z.string().optional(),
    sendStatus: z.enum(['UNSENT', 'SENT', 'STOPPED'] as const).default('UNSENT'),
  }),
)

const TriggerSendUpdateSchema = toTypedSchema(
  z.object({
    expectedSendTime: z.date({
      required_error: '请选择预期发送时间',
    }),
    sendTime: z.date().optional(),
    sendUserId: z.array(z.string().min(1)).min(1, '至少选择一个响应人').default([]),
    reportUserId: z.array(z.string().min(1)).min(1, '至少选择一个告知人').default([]),
    triggerRecordId: z.string().min(1, '请输入触发记录ID'),
    sendInfo: z.string().optional(),
    sendResult: z.string().optional(),
    sendStatus: z.enum(['UNSENT', 'SENT', 'STOPPED'] as const).default('UNSENT'),
  }),
)

export function useFeedbackTriggerSendSearchForm() {
  const form = useForm<FeedbackTriggerSendSearchParam>({
    validationSchema: TriggerSendSearchSchema,
  })
  return form
}

export function useFeedbackTriggerSendCreateForm() {
  const form = useForm({
    validationSchema: TriggerSendCreateSchema,
  })
  return form
}

export function useFeedbackTriggerSendUpdateForm() {
  const form = useForm({
    validationSchema: TriggerSendUpdateSchema,
  })
  return form
}
