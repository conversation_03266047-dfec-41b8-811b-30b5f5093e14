<script setup lang="ts">
import { useProductEditForm } from './schema'
import type { Product } from '~/api/product_num/types'
import { productApi } from '~/api/product_num'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit, setValues } = useProductEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await productApi.update(props.id, values)
      success('创建成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const product: Product = await productApi.get(props.id)
    setValues(product)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建产品" @show="loadData">
    <form @submit="save">
      <FormLayout>
        <LInput name="lineCode" label="线体编码" />
        <LInput name="productModel" label="产品型号" />
        <LInputNumber name="pointNum" label="点数" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
