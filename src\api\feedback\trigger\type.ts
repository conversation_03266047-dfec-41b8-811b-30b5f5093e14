/**
 * 快返触发记录表
 */
export interface FeedbackTriggerRecord {
  anomaliesCode: string // 异常编码
  anomaliesName: string // 异常名称
  lineCode: string // 线体编码
  anomaliesDetail: string // 异常明细
  triggerTime: Date // 触发时间 - 使用 string 类型表示 ISO 格式日期（例如：'2023-09-15T10:00:00Z'）
  triggerUserId: string // 触发人ID
  triggerUserName: string // 触发人名称
  triggerClose: boolean // 异常是否关闭
  triggerCloseTime: Date // 异常关闭时间
}

export interface NoticeUser {
  noticeUserId: string
  noticeUserName?: string
}

export interface FeedbackTriggerRecordCreateParam {
  anomaliesCode: string // 异常编码
  anomaliesName?: string // 异常名称
  lineCode: string // 线体编码
  anomaliesDetail: string // 异常明细
  anomaliesStartTime: Date // 异常开始时间 - 使用 string 类型表示 ISO 格式日期（例如：'2023-09-15T10:00:00Z'）
  noticeUsers: NoticeUser[] // 通知用户列表
}

export interface FeedbackTriggerRecordWithId extends FeedbackTriggerRecord {
  id: string // 主键ID
}

export interface FeedbackTriggerRecordWithNoticeUser extends FeedbackTriggerRecord {
  id: string // 主键ID
  noticeUsers: NoticeUser[] // 通知用户
}

export interface FeedbackTriggerSolution {
  solution: string // 解决方案
  solverId: string // 解决人ID
  solverName: string // 解决人名称
  solveTime: string // 解决时间
}

export interface FeedbackTriggerWithSolution {
  id: string // 主键ID
  solutions: FeedbackTriggerSolution[] // 解决方案列表
}
