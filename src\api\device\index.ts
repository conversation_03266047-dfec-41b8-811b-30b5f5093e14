import type { PageList, Pageable } from '../common/type'
import type { StatusResponse } from './type'
import type { Devi<PERSON>, DeviceCreateParam, DeviceQuery } from './types'
import { kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const deviceApi = {
  // 获取设备对应的客户端状态
  getClientStatus: (code: string) => kyGet(`device/status/${code}`).json<StatusResponse>(),
  create: (param: DeviceCreateParam) => kyPost('device', param),
  page: (param: Pageable<Partial<DeviceQuery>>) => kyPost('device/page', param).json<PageList<Device>>(),
  delete: (id: string) => kyDelete(`device/${id}`),
  get: (id: string) => kyGet(`device/${id}`).json<Device>(),
  update: (id: string, param: DeviceCreateParam) => kyPut(`device/${id}`, param),
  batchDelete: (ids: string[]) => kyDelete('device/batch', { json: ids }),
}
