<script setup lang="ts">
import { z } from 'zod'
import { toTypedSchema } from '@vee-validate/zod'
import { useField, useForm } from 'vee-validate'
import { analyzeApi } from '~/api/analyze'
import type { AnalyzeQuery } from '~/api/analyze/type'
import { workshopApi } from '~/api/workshop'
import type { Workshop } from '~/layout/index/types'
import { handleFileExport } from '~/utils/export'

// 线体类型选项
// const lineTypeOptions = [
//   { label: '单轨', value: LineType.singleTrack },
//   { label: '双轨', value: LineType.dualTrack },
// ]

// 表单验证schema
const exportSchema = toTypedSchema(z.object({
  workShopId: z.string().min(1, '请选择车间'),
  // code: z.string().min(1, '请输入线体编码'),
  // lineType: z.nativeEnum(LineType).optional(),
  // productModel: z.string().optional(),
  selectedDate: z.date().optional(),
  startTime: z.date().optional(),
  endTime: z.date().optional(),
}))

// 表单状态
const { handleSubmit, resetForm, setFieldValue } = useForm({
  validationSchema: exportSchema,
  initialValues: {
    workShopId: '',
    code: '',
    lineType: undefined,
    productModel: '',
    selectedDate: undefined,
    startTime: undefined,
    endTime: undefined,
  },
})

// 页面状态
const loading = ref(false)
const workshops = ref<Workshop[]>([])

// 获取车间列表
async function loadWorkshops() {
  try {
    workshops.value = await workshopApi.list()
  }
  catch (error) {
    console.error('获取车间列表失败:', error)
  }
}

// 车间选项
const workshopOptions = computed(() =>
  workshops.value.map(workshop => ({
    label: workshop.name,
    value: workshop.code,
  })),
)

// 监听日期选择变化
const { value: selectedDate } = useField('selectedDate')
watch(selectedDate, (newDate) => {
  if (newDate) {
    // 设置开始时间为当天8点
    const startTime = new Date(newDate)
    startTime.setHours(8, 0, 0, 0)

    // 设置结束时间为次日8点
    const endTime = new Date(newDate)
    endTime.setDate(endTime.getDate() + 1)
    endTime.setHours(8, 0, 0, 0)

    setFieldValue('startTime', startTime)
    setFieldValue('endTime', endTime)
  }
})

// 导出Excel
const exportExcel = handleSubmit(async (values) => {
  try {
    const { workShopId, selectedDate, ...queryParams } = values
    const analyzeQuery: AnalyzeQuery = {
      code: queryParams.code,
      // lineType: queryParams.lineType,
      // productModel: queryParams.productModel || undefined,
      startTime: queryParams.startTime,
      endTime: queryParams.endTime,
    }

    // 确定用于文件名的日期，如果未选择，则使用当天日期
    const dateForFilename = selectedDate || new Date()

    // 格式化日期为 YYYY-MM-DD
    const year = dateForFilename.getFullYear()
    const month = String(dateForFilename.getMonth() + 1).padStart(2, '0')
    const day = String(dateForFilename.getDate()).padStart(2, '0')
    const formattedDate = `${year}-${month}-${day}`

    // 获取车间名称
    const workshopName = workshops.value.find(w => w.code === workShopId)?.name || workShopId

    // 构造最终文件名
    const filename = `${workshopName}(${formattedDate})oee数据.xlsx`

    await handleFileExport(
      () => analyzeApi.exportDailyExcel(workShopId, analyzeQuery),
      filename, // 使用动态生成的文件名
      isLoading => loading.value = isLoading,
    )
  }
  catch (error) {
    console.error('导出失败:', error)
  }
})

// 重置表单
function handleReset() {
  resetForm()
}

// 页面初始化
onMounted(() => {
  loadWorkshops()
})
</script>

<template>
  <PageContainer>
    <div class="mb-6 text-2xl font-bold">
      车间日报Excel导出
    </div>

    <div class="p-6 card">
      <form @submit="exportExcel">
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <!-- 车间选择 -->
          <FSelect
            name="workShopId"
            label="车间"
            :options="workshopOptions"
          />

          <!-- 线体编码 -->
          <!-- <FInput
            name="code"
            label="线体编码"
          /> -->

          <!-- 线体类型 -->
          <!-- <FSelect
            name="lineType"
            label="线体类型"
            :options="lineTypeOptions"
          /> -->

          <!-- 产品型号 -->
          <!-- <FInput
            name="productModel"
            label="产品型号"
          /> -->

          <!-- 快速日期选择 -->
          <FDatePicker
            name="selectedDate"
            label="日期选择"
            placeholder="选择日期自动设置时间范围"
            :date-props="{ showTime: false }"
          />

          <!-- 开始时间 -->
          <!-- <FDatePicker
            name="startTime"
            label="开始时间"
            :date-props="{ showTime: true, hourFormat: '24' }"
          /> -->

          <!-- 结束时间 -->
          <!-- <FDatePicker
            name="endTime"
            label="结束时间"
            :date-props="{ showTime: true, hourFormat: '24' }"
          /> -->
        </div>

        <!-- 操作按钮 -->
        <div class="mt-8 flex gap-4">
          <Button
            type="submit"
            label="导出Excel"
            icon="pi pi-download"
            :loading="loading"
            :disabled="loading"
          />
          <Button
            type="button"
            label="重置"
            icon="pi pi-refresh"
            severity="secondary"
            outlined
            @click="handleReset"
          />
        </div>
      </form>
    </div>
  </PageContainer>
</template>

<style scoped>
.card {
  background: var(--surface-card);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
}
</style>
