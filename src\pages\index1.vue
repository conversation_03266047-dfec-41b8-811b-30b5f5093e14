<script setup lang="ts">
import { useRoute } from 'vue-router'
import { ref, watch } from 'vue'
import LineContainer1 from '~/components/oee/LineContainer1.vue'
import { useAggregationStore } from '~/stores/aggregation'
import Honghong from '~/assets/honghong.gif'

const startTime = ref<Date>()
const endTime = ref<Date>()
const route = useRoute()
const isDark = ref(false)
const loading = ref<boolean>(false)
const aggregationStore = useAggregationStore()
const refreshInterval = ref<ReturnType<typeof setInterval>>()
const el = ref<HTMLElement | null>(null)
const { width, height } = useWindowSize()
const { style } = useDraggable(el, {
  initialValue: { x: width.value * 0.01, y: height.value * 0.75 },
})

// 数据状态
const lineList = ref<{ code: string, name: string }[]>([])

function generateMockData(code: string) {
  const mockData = {
    'WH-001': [
      { code: 'SMT1-1', name: '线体' },
      { code: 'SMT1-2', name: '线体' },
      { code: 'SMT1-3', name: '线体' },
      { code: 'SMT1-4', name: '线体' },
      { code: 'SMT1-5', name: '线体' },
    ],
    'WH-002': [],
    'WH-003': [],
    'LS-001': [
      { code: 'LS1-1', name: '线体' },
      { code: 'LS1-2', name: '线体' },
      { code: 'LS1-3', name: '线体' },
      { code: 'LS1-4', name: '线体' },
      { code: 'LS1-5', name: '线体' },
    ],
  }

  return mockData[code as keyof typeof mockData] || []
}

async function loadData(code: string) {
  loading.value = true
  try {
    lineList.value = generateMockData(code)
  }
  finally {
    loading.value = false
  }
}

// 日期处理方法
async function initializeDates() {
  loading.value = true
  try {
    const now = new Date()
    const currentDay = new Date(now)
    const nextDay = new Date(now)
    nextDay.setDate(now.getDate() + 1)

    startTime.value = new Date(currentDay.setHours(8, 0, 0, 0))
    endTime.value = new Date(nextDay.setHours(8, 0, 0, 0))
  }
  catch (error) {
    console.error('Failed to get server time:', error)
    // 使用当前时间
    const now = new Date()
    const currentDay = new Date(now)
    const nextDay = new Date(now)
    nextDay.setDate(now.getDate() + 1)

    startTime.value = new Date(currentDay.setHours(8, 0, 0, 0))
    endTime.value = new Date(nextDay.setHours(8, 0, 0, 0))
  }
  finally {
    loading.value = false
  }
}

function checkAndUpdateDates() {
  const now = new Date()
  if (!endTime.value || now > endTime.value) {
    initializeDates()
  }
}

// 监听车间切换
watch(() => route.params.code, (newCode) => {
  if (newCode) {
    const code = Array.isArray(newCode) ? newCode[0] : newCode
    initializeDates()
    loadData(code)
  }
}, { immediate: true })

onBeforeMount(() => {
  initializeDates()

  const initialCode = route.params.code
  if (initialCode) {
    const code = Array.isArray(initialCode) ? initialCode[0] : initialCode
    loadData(code)
  }
  refreshInterval.value = setInterval(() => {
    checkAndUpdateDates()
    const currentCode = route.params.code
    if (currentCode) {
      const code = Array.isArray(currentCode) ? currentCode[0] : currentCode
      loadData(code)
    }
  }, 300000)
})

function resetDates() {
  initializeDates()
  const currentCode = route.params.code
  if (currentCode) {
    const code = Array.isArray(currentCode) ? currentCode[0] : currentCode
    loadData(code)
  }
}
</script>

<template>
  <div class="w-full" :class="{ light: !isDark, dark: isDark }">
    <img ref="el" :src="Honghong" alt="Logo" class="fixed z-99 w-32 cursor-grab" :style="style">
    <form v-show="aggregationStore.isSearchVisible" class="mt-2 flex justify-end gap-4 px-8">
      <DatePicker
        v-model="startTime" fluid show-icon date-format="yy-mm-dd"
        v-bind="{ showTime: true, hourFormat: '24' }"
      >
        <template #inputicon="slotProps">
          <i class="pi pi-clock" @click="slotProps.clickCallback" />
        </template>
      </DatePicker>
      <DatePicker
        v-model="endTime" fluid show-icon date-format="yy-mm-dd"
        v-bind="{ showTime: true, hourFormat: '24' }"
      >
        <template #inputicon="slotProps">
          <i class="pi pi-clock" @click="slotProps.clickCallback" />
        </template>
      </DatePicker>
      <Button size="small" icon="pi pi-refresh" @click="resetDates" />
    </form>

    <div class="flex flex-col items-center">
      <LineContainer1
        v-for="item in lineList"
        :key="item.code"
        :code="item.code"
        :start-time="startTime!"
        :end-time="endTime!"
      />
    </div>
  </div>
</template>
