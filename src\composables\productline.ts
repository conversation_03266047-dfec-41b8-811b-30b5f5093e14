import { HTTPError } from 'ky'
import { productLineApi } from '~/api/line'
import type { Productline } from '~/layout/dashboard/productline'
import { useLayoutStore } from '~/stores/layout'

interface LineState {
  staticLineDesktopInactive: boolean
  overlayLineActive: boolean
  profileSidebarVisible: boolean
  configSidebarVisible: boolean
  staticLineMobileActive: boolean
  lineHoverActive: boolean
  activeLineItemKey: null | string
}

const productlineValue = ref<Productline[]>([])

const lineState = reactive<LineState>({
  staticLineDesktopInactive: false,
  overlayLineActive: false,
  profileSidebarVisible: false,
  configSidebarVisible: false,
  staticLineMobileActive: false,
  lineHoverActive: false,
  activeLineItemKey: null,
})

const activeLineItem = computed(() => {
  return productlineValue.value.find(o => o.code === unref(lineState.activeLineItemKey)) || null
})

async function init() {
  try {
    const res = await productLineApi.list()
    productlineValue.value = res
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(ex.message)
    }
  }
}

init()

export function useProductline() {
  // 切换产品线
  const onProductlineToggle = () => {
    const { layoutConfig } = useLayoutStore()
    if (layoutConfig.menuMode === 'overlay') {
      lineState.overlayLineActive = !lineState.overlayLineActive
    }

    if (window.innerWidth > 991) {
      lineState.staticLineDesktopInactive = !lineState.staticLineDesktopInactive
    }
    else {
      lineState.staticLineMobileActive = !lineState.staticLineMobileActive
    }
  }

  // 设置产品线
  const setActiveLineItem = async (itemKey: MaybeRef<string | null>) => {
    lineState.activeLineItemKey = unref(itemKey)
  }

  // 重置产品线
  const resetLine = () => {
    lineState.overlayLineActive = false
    lineState.staticLineMobileActive = false
    lineState.lineHoverActive = false
  }

  const isSidebarActive = computed(() => lineState.overlayLineActive || lineState.staticLineMobileActive)
  const line = computed(() => productlineValue.value)

  return { setActiveLineItem, resetLine, line, isSidebarActive, onProductlineToggle, lineState, activeLineItem, init }
}
