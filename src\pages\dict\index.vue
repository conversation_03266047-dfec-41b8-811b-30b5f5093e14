<script setup lang="ts">
import { type DataTablePageEvent, useConfirm } from 'primevue'
import { useDictSearchForm } from './schema'
import DictCreate from './DictCreate.vue'
import DictEdit from './DictEdit.vue'
import PageContainer from '~/components/common/PageContainer.vue'
import type { PageData } from '~/api/common/type'
import type { DictSimpleWithId } from '~/api/common/dict/types'
import { dictApi } from '~/api/common/dict'

const open = ref({
  create: false,
  edit: false,
})
const editId = ref<string>()

const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

const total = ref(0)

const loading = ref<boolean>(false)

const data = ref<DictSimpleWithId[]>([])

const confirm = useConfirm()
const searchForm = useDictSearchForm()

const search = searchForm.handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await dictApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

async function del(id: string) {
  await dictApi.delete(id)
  success('删除成功')
}

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

function openEditPage(id: string) {
  editId.value = id
  open.value.edit = true
}

function openCreatePage() {
  open.value.create = true
}

function confirmDel(id: string, event: any) {
  confirm.require({
    group: 'delete',
    target: event.currentTarget,
    message: '确认删除？',
    accept: async () => {
      await del(id)
      success('删除成功')
      data.value = data.value.filter(o => o.id !== id)
    },
  })
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer class="mt-4">
    <SearchBox :loading="loading" @submit="search">
      <FInput name="name" label="名称" />
      <FInput name="code" label="编码" />
    </SearchBox>
    <div class="pl-4">
      <Button icon="pi pi-plus" outlined size="small" @click="openCreatePage" />
    </div>
    <DataTable class="p-4" :value="data" lazy paginator data-key="id" :rows="pageData.pageSize" :total-records="total" @page="page">
      <Column field="id" header="ID" />
      <Column field="name" header="名称" />
      <Column field="code" header="编码" />

      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined icon="pi pi-pencil" size="small" @click="openEditPage(slotProps.data.id)" />
            <Button outlined icon="pi pi-trash" size="small" severity="danger" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column>
    </DataTable>
    <DictCreate v-model:open="open.create" @save="search" />
    <DictEdit :id="editId" v-model:open="open.edit" @save="search" />
  </PageContainer>
</template>
