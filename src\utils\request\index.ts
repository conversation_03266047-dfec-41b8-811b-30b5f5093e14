import type { HTTPError, KyRequest } from 'ky'
import ky from 'ky'
import { useRouter } from 'vue-router'
import { useUserStore } from '~/stores/user'

// todo optimize token refresh

interface ErrResponse {
  code: string
  msg: string
}

const api = ky.extend({
  retry: 0,
  prefixUrl: import.meta.env.VITE_APP_BASE_API,
  timeout: import.meta.env.DEV ? 90000 : 90000,
  parseJson: parseISOString2Date,
  stringifyJson: stringifyDate2ISOString,
  hooks: {
    beforeRequest: [
      addAuth,
    ],
    afterResponse: [

    ],
    beforeError: [
      getErrResponse,
      handleUnAuthorized,
      toastError,
    ],
  },
})

async function getErrResponse(error: HTTPError) {
  try {
    const res = await error.response.json<ErrResponse>()
    error.message = res.msg
    error.name = res.code
    return error
  }
  catch {
    return error
  }
}

function toastError(err: HTTPError) {
  errorToast({
    summary: err.name,
    detail: err.message,
    life: 3000,
  })
  return err
}

function addAuth(request: KyRequest) {
  if (request.credentials === 'same-origin') {
    const accessToken = localStorage.getItem(import.meta.env.VITE_APP_ACCESS_STORE)
    if (accessToken) {
      request.headers.set(import.meta.env.VITE_APP_AUTH_HEADER, `Bearer ${accessToken}`)
    }
  }
}

function parseISOString2Date(text: string) {
  return JSON.parse(text, (key: string, value: any) => {
    const isoDateFormat = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d*)?(?:[-+]\d{2}:?\d{2}|Z)?$/
    if (typeof value === 'string' && isoDateFormat.test(value)) {
      return new Date(value)
    }
    return value
  })
}

function stringifyDate2ISOString(object: any) {
  return JSON.stringify(object, (key: string, value: any) => {
    if (value instanceof Date) {
      return value.toISOString()
    }
    return value
  })
}

const kyGet = function (url: string, param?: object) {
  const params = new URLSearchParams()
  if (param) {
    for (const entry of Object.entries(param)) {
      if (entry[1] instanceof Date) {
        params.set(entry[0], entry[1].toISOString())
      }
      else if (typeof entry[1] === 'object') {
        params.set(entry[0], stringifyDate2ISOString(entry[1]))
      }
      else if (entry[1] === undefined || entry[1] === null) {
        continue
      }
      else {
        params.set(entry[0], entry[1])
      }
    }
    return api.get(url, {
      searchParams: params,
    })
  }
  return api.get(url)
}

const kyPost = function (url: string, data?: any, param?: object) {
  const params = new URLSearchParams()
  if (param) {
    for (const entry of Object.entries(param)) {
      if (entry[1] instanceof Date) {
        params.set(entry[0], entry[1].toISOString())
      }
      else if (typeof entry[1] === 'object') {
        params.set(entry[0], stringifyDate2ISOString(entry[1]))
      }
      else {
        params.set(entry[0], entry[1])
      }
    }
  }
  return api.post(url, {
    json: data,
    searchParams: params,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

const kyPut = function (url: string, data?: any, param?: object) {
  const params = new URLSearchParams()
  if (param) {
    for (const entry of Object.entries(param)) {
      if (entry[1] instanceof Date) {
        params.set(entry[0], entry[1].toISOString())
      }
      else if (typeof entry[1] === 'object') {
        params.set(entry[0], stringifyDate2ISOString(entry[1]))
      }
      else {
        params.set(entry[0], entry[1])
      }
    }
  }
  return api.put(url, {
    json: data,
    searchParams: params,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

const kyDelete = function (url: string, param?: object) {
  const params = new URLSearchParams()
  if (param) {
    for (const entry of Object.entries(param)) {
      if (entry[1] instanceof Date) {
        params.set(entry[0], entry[1].toISOString())
      }
      else if (typeof entry[1] === 'object') {
        params.set(entry[0], stringifyDate2ISOString(entry[1]))
      }
      else if (entry[1] === undefined || entry[1] === null) {
        continue
      }
      else {
        params.set(entry[0], entry[1])
      }
    }
    return api.delete(url, {
      searchParams: params,
    })
  }
  return api.delete(url)
}

const kyBatchDelete = function (url: string, options?: any) {
  return api.delete(url, options)
}

const kyPostFile = function (url: string, data: FormData, param?: object) {
  const params = new URLSearchParams()
  if (param) {
    for (const entry of Object.entries(param)) {
      if (entry[1] instanceof Date) {
        params.set(entry[0], entry[1].toISOString())
      }
      else if (typeof entry[1] === 'object') {
        params.set(entry[0], stringifyDate2ISOString(entry[1]))
      }
      else {
        params.set(entry[0], entry[1])
      }
    }
  }

  return api.post(url, {
    body: data,
    searchParams: params,
  })
}

async function handleUnAuthorized(err: HTTPError) {
  if (err.response.status === 401) {
    const router = useRouter()
    const userStore = useUserStore()
    userStore.clear()
    router.push('/login')
  }
  return err
}

export { api as kyApi, kyGet, kyPost, kyPut, kyDelete, kyBatchDelete, kyPostFile }
