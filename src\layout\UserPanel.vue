<script setup lang="ts">
import { storeToRefs } from 'pinia'
import Avatar from 'primevue/avatar'
import { useRouter } from 'vue-router'
import Logo from '~/assets/logo.svg'
import { useUserStore } from '~/stores/user'

const { user } = storeToRefs(useUserStore())
const { logout } = useUserStore()
const router = useRouter()

// 登出并跳转到登录页
function logoutAndToLoginPage() {
  logout()
  router.push('/login')
}

// 修改密码，跳转到新页面
function changePassword() {
  router.push('/user/changePassword')
}
</script>

<template>
  <div class="flex flex-col gap-4">
    <div class="flex items-center gap-4">
      <Avatar :image="Logo" shape="circle" size="large" />
      <div>{{ user?.displayName }}</div>
    </div>
    <div>
      <ul class="m-0 flex flex-col list-none p-0">
        <li class="flex cursor-pointer items-center gap-2 rounded px-2 py-3 hover:bg-content-hover-background" @click.stop="changePassword()">
          <div>
            <div class="text-surface-500 dark:text-surface-400">
              修改密码
            </div>
          </div>
        </li>
        <li class="flex cursor-pointer items-center gap-2 rounded px-2 py-3 hover:bg-content-hover-background" @click="logoutAndToLoginPage()">
          <div>
            <div class="text-surface-500 dark:text-surface-400">
              登出
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
