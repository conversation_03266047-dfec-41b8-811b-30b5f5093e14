interface Index {
  format: string
  version: string
  machine: string
  date: Date
  authortype: string
  author: string
}

interface Information {
  stage: string
  lane: string
  serial: string
  code: string
  bcrstatus: string
}

interface Generaltime {
  total: number
  actual: number
  actualnpm: number
}

interface GeneralCount {
  board: number
  module: number
}

interface StopTime {
  total: number
  fwait: number
  rwait: number
  scstop: number
  pwait: number
  trbl: number
  scestop: number
  othrstop: number
  crerr: number
  cterr: number
  cnvstop: number
  brcgstop: number
}

interface StopCount {
  total: number
  fwait: number
  rwait: number
  scstop: number
  pwait: number
  trbl: number
  scestop: number
  othrstop: number
  crerr: number
  cterr: number
  cnvstop: number
  brcgstop: number
}

interface Takeupunit {
  head: number
  unit: number
  side: number
  tcnt: number
}

interface Takeupnozzle {
  head: number
  nozzlestocker: number
  nozzlechanger: number
  tcnt: number
}

interface Missunit {
  head: number
  unit: number
  side: number
  tmiss: number
  rmiss: number
  hmiss: number
  dmiss: number
  mmiss: number
  rsmiss: number
}

interface Missnozzle {
  head: number
  nozzlestocker: number
  nozzlechanger: number
  tmiss: number
  rmiss: number
  hmiss: number
  dmiss: number
  mmiss: number
  rsmiss: number
}

interface Link {
  mcdfile: string
}

export interface NpmSmtLog {
  id: string
  filename: string
  logtime: Date
  index: Index
  information: Information
  generaltime: Generaltime
  generalcount: GeneralCount
  stoptime: StopTime
  stopcount: StopCount
  takeupunits: Takeupunit[]
  takeupnozzles: Takeupnozzle[]
  missunits: Missunit[]
  missnozzles: Missnozzle[]
  link: Link
}
