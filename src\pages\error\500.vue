<script setup lang="ts">
import Button from 'primevue/button'
import { useRouter } from 'vue-router'

const router = useRouter()

function toHomePage() {
  router.push('/')
}
</script>

<template>
  <div class="h-full flex flex-col items-center justify-center gap-4">
    <div class="text-xl">
      500 SERVER INTERNAL ERROR
    </div>
    <Button @click="toHomePage">
      返回首页
    </Button>
  </div>
</template>
