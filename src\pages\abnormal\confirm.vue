<script setup lang="ts">
import { useAbnormalConfirmForm } from './schema'
import { abnormalApi } from '~/api/abnormal'
import type { Abnormal } from '~/api/abnormal/types'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit, setValues } = useAbnormalConfirmForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await abnormalApi.confirm(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const abnormal: Abnormal = await abnormalApi.get(props.id)
    setValues(abnormal)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="异常确认" @show="loadData">
    <form @submit="save">
      <FormVertical>
        <LInput name="confirm" label="确认人" />
      </FormVertical>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
