import type { FeedbackTriggerR<PERSON>ord, FeedbackTriggerRecordCreateParam, FeedbackTriggerRecordWithNoticeUser } from './type'
import type { PageList, Pageable } from '~/api/common/type'
import { kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const TriggerRecordApi = {
  create: (param: FeedbackTriggerRecordCreateParam) => kyPost('feedback-trigger-records', param),
  page: (param: Pageable<Partial<FeedbackTriggerRecordWithNoticeUser>>) => kyPost('feedback-trigger-records/page', param).json<PageList<FeedbackTriggerRecordWithNoticeUser>>(),
  delete: (id: string) => kyDelete(`feedback-trigger-records/${id}`),
  hasOpenExceptions: (lineCode: string) => kyGet(`feedback-trigger-records/has-open-exceptions/${lineCode}`).text(),
  latestOpenException: (lineCode: string) => kyGet(`feedback-trigger-records/latest-open-exception/${lineCode}`).json<FeedbackTriggerRecord>(),
  closeException: (id: string) => kyPut(`feedback-trigger-records/${id}/close`),
  batchCloseException: (ids: string[]) => kyPost('feedback-trigger-records/batch-close', ids),
  get: (id: string) => kyGet(`feedback-trigger-records/${id}`).json<FeedbackTriggerRecord>(),
  update: (id: string, param: Partial<FeedbackTriggerRecord>) => kyPut(`feedback-trigger-records/${id}`, param),
}
