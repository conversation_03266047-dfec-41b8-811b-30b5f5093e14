<script setup lang="ts">
import { z } from 'zod'
import Button from 'primevue/button'

import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'

const targetEnum = z.enum([
  '_blank',
  '_self',
  '_parent',
])

const targetOptions = targetEnum.options

const schema = toTypedSchema(z.object({
  path: z.string().startsWith('/'),
  title: z.string().min(1),
  target: targetEnum,
  date: z.date(),
  redirect: z.boolean().default(false),
}))

const { resetForm, handleSubmit } = useForm({
  validationSchema: schema,
})

const { value } = useField<boolean>('redirect')

const save = handleSubmit((values) => {
  info(JSON.stringify(values))
})
</script>

<template>
  <div>
    <form class="w-md p-4" @submit="save">
      <FInput name="path" label="路径" />
      <FSelect name="target" label="target" :options="targetOptions" />
      <FInput name="title" label="标题" />
      <FDatePicker name="date" label="日期" />
      <FormItem name="redirect" label="redirect">
        <ToggleSwitch id="redirect" v-model="value" name="redirect" />
      </FormItem>
      <Button type="submit">
        保存
      </Button>
      <Button severity="second" @click="resetForm()">
        清空
      </Button>
    </form>
    <div class="h-screen">
      123
    </div>
  </div>
</template>
