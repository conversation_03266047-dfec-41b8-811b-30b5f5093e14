{"type": "module", "private": true, "packageManager": "pnpm@9.6.0", "scripts": {"build": "vite build", "clean": "rimraf node_modules", "dev": "vite --port 3333 --open", "lint": "eslint . --fix", "typecheck": "vue-tsc --noEmit", "preview": "vite preview", "test": "vitest", "up": "taze major -I", "prepare": "husky"}, "dependencies": {"@primeuix/themes": "^1.0.0", "@vee-validate/zod": "^4.14.7", "@vueuse/core": "^10.11.1", "casdoor-vue-sdk": "^1.6.0", "date-fns": "^4.1.0", "echarts": "^5.5.1", "echarts-liquidfill": "^3.1.0", "i18next": "^24.2.1", "ky": "^1.7.2", "pinia": "^2.2.8", "pinia-plugin-persistedstate-2": "^2.0.27", "primeicons": "^7.0.0", "primevue": "^4.3.1", "screenfull": "^6.0.2", "vee-validate": "^4.14.7", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "zod": "^3.23.8", "zod-i18n-map": "^2.27.0"}, "devDependencies": {"@antfu/eslint-config": "^2.27.3", "@iconify-json/carbon": "^1.2.4", "@iconify-json/ic": "^1.2.1", "@iconify-json/ri": "^1.2.3", "@primevue/auto-import-resolver": "^4.3.1", "@types/node": "^22.10.1", "@unocss/eslint-config": "^0.61.9", "@unocss/eslint-plugin": "^0.61.9", "@unocss/reset": "^0.61.9", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "eslint": "^9.15.0", "eslint-plugin-format": "^0.1.2", "husky": "^9.1.7", "jsdom": "^24.1.3", "lint-staged": "^15.2.10", "pnpm": "^9.14.3", "simple-git-hooks": "^2.11.1", "taze": "^0.16.9", "typescript": "^5.7.2", "unocss": "^0.61.9", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^5.4.11", "vitest": "^2.1.6", "vue-tsc": "^2.1.10"}, "lint-staged": {"*": "eslint --fix"}}