import type { App } from 'vue'
import { useUserStore } from '~/stores/user'

export function setupPermissionDirective(app: App) {
  app.directive('permission', {
    mounted(el: HTMLElement, binding: { value: string }) {
      const userStore = useUserStore()
      // 如果是管理员或者具有特定权限，则显示元素
      if (!(userStore.isAdmin || userStore.perms?.includes(binding.value))) {
        el.parentNode?.removeChild(el)
      }
    },
  })
}
