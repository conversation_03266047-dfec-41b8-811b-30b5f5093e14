import { toTypedSchema } from '@vee-validate/zod'

// 查询理论值
const PlannedCapacitySearchSchema = toTypedSchema(z.object({
  lineCode: z.string().optional(),
  productModel: z.string().optional(),
}))

// 创建理论值
const PlannedCapacityCreateSchema = toTypedSchema(z.object({
  lineCode: z.string({
    required_error: '请输入线体编码',
  }),
  productModel: z.string({
    required_error: '请输入产品型号',
  }),
  plannedQuantity: z.number({
    required_error: '请输入计划产能',
  }),
}))

const PlannedCapacityEditSchema = toTypedSchema(z.object({
  lineCode: z.string({
    required_error: '请输入线体编码',
  }),
  productModel: z.string({
    required_error: '请输入产品型号',
  }),
  plannedQuantity: z.number({
    required_error: '请输入计划产能',
  }),
}))

export function usePlannedCapacitySearchForm() {
  const form = useForm({
    validationSchema: PlannedCapacitySearchSchema,
  })
  return form
}

export function usePlannedCapacityCreateForm() {
  const form = useForm({
    validationSchema: PlannedCapacityCreateSchema,
  })
  return form
}

export function usePlannedCapacityEditForm() {
  const form = useForm({
    validationSchema: PlannedCapacityEditSchema,
  })
  return form
}
