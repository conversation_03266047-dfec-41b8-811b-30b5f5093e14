<script setup lang="ts">
import { type DeviceInfo, DeviceType } from '~/api/line/types'

import aoi from '~/assets/images/aoi.png'
import npm from '~/assets/images/npm.png'
import print from '~/assets/images/print.png'
import samsung from '~/assets/images/samsung_smt.png'
import yamaha from '~/assets/images/yamaha.png'

defineProps<{
  device: DeviceInfo
}>()

const imageMap = new Map<DeviceType, string>()
imageMap.set(DeviceType.AOI_JUZTE, aoi)
imageMap.set(DeviceType.PRINT_GKG, print)
imageMap.set(DeviceType.SMT_NPM, npm)
imageMap.set(DeviceType.SMT_NPM2, npm)
imageMap.set(DeviceType.SMT_SAMSUNG, samsung)
imageMap.set(DeviceType.SMT_YAMAHA, yamaha)
imageMap.set(DeviceType.AOI_YAMAHA, aoi)
</script>

<template>
  <div class="m-2 max-w-md border rounded">
    <div class="mb-2 w-full flex justify-center border border-primary rounded py-2 text-xl text-primary font-bold">
      {{ device.name }}
    </div>
    <div class="flex gap-8 p-4">
      <Image :src="imageMap.get(device.type)" image-class="h-32" class="grow-1" />
      <div class="grid grid-cols-[1fr_2fr] grow-1">
        <div>实际CT:</div>
        <div class="text-primary">
          {{ device.ct }} s
        </div>
        <!-- <div>理论CT:</div>
        <div class="text-primary">
          {{ device.ct }} s
        </div> -->
        <div>编号:</div>
        <div>
          {{ device.code }}
        </div>
        <div />
      </div>
    </div>
  </div>
</template>
