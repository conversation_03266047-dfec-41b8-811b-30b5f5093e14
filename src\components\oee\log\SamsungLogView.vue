<script setup lang="ts">
import { formatDate } from '@vueuse/core'
import { HTTPError } from 'ky'
import { logApi } from '~/api/log'

import type { SamsungSmtLog } from '~/api/log/device/samsungSmt'

const props = defineProps<{
  deviceCode: string
  id: string
}>()

const data = ref<SamsungSmtLog>()

watch(() => props, async () => {
  search()
})

async function search() {
  try {
    const res = await logApi.getProductionLog({
      deviceCode: props.deviceCode,
      id: props.id,
    })
    data.value = res
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(ex.message)
    }
  }
}

onMounted(() => {
  search()
})
</script>

<template>
  <div class="w-full p-4">
    <div class="mb-4 w-full flex justify-center text-2xl font-bold">
      NPM SMT LOG
    </div>
    <Panel header="基本信息" class="mb-4 w-full">
      <div class="grid grid-cols-4 gap-4 p-4">
        <div> 日志名称 </div><div>{{ data?.filename }}</div>
        <div>日志时间</div><div>{{ data?.logtime ? formatDate(data?.logtime, 'YYYY-MM-DD HH:mm:ss') : '' }}</div>
        <div>产品名</div><div>{{ data?.productname }}</div>
        <div>开始时间</div><div>{{ data ? formatDate(data.starttime, 'YYYY-MM-DD HH:mm:ss') : '' }}</div>
        <div>结束时间</div><div>{{ data ? formatDate(data.endtime, 'YYYY-MM-DD HH:mm:ss') : '' }}</div>
      </div>
    </Panel>
    <Panel header="概要" class="mb-4 w-full">
      <div class="grid grid-cols-8 gap-4 p-4">
        <div>版本号</div><div>{{ data?.samsungindex. version }}</div>
        <div>开机时间（单位：秒）</div><div>{{ data?.samsungindex. powertime }}</div>
        <div>放置时间</div><div>{{ data?.samsungindex. placetime }}</div>
        <div>等待时间</div><div>{{ data?.samsungindex. waittime }}</div>
        <div>运行时间 = 放置时间 + 等待时间 + 转移时间</div><div>{{ data?.samsungindex. runtime }}</div>
        <div>停止时间</div><div>{{ data?.samsungindex. stoptime }}</div>
        <div>空闲时间</div><div>{{ data?.samsungindex. idletime }}</div>
        <div>输入等待时间</div><div>{{ data?.samsungindex. inwaittime }}</div>
        <div>输出等待时间</div><div>{{ data?.samsungindex. outwaittime }}</div>
        <div>转移时间</div><div>{{ data?.samsungindex. transtime }}</div>
        <div>错误停止时间</div><div>{{ data?.samsungindex. wrongstoptime }}</div>
        <div>错误时间停止</div><div>{{ data?.samsungindex. errorstoptime }}</div>
        <div>错误停止计数</div><div>{{ data?.samsungindex. wrongstopcount }}</div>
        <div>错误停止计数</div><div>{{ data?.samsungindex. errorstopcount }}</div>
        <div>面板输入计数</div><div>{{ data?.samsungindex. panelincount }}</div>
        <div>面板输出计数</div><div>{{ data?.samsungindex. paneloutcount }}</div>
        <div>面板计数</div><div>{{ data?.samsungindex. panelcount }}</div>
        <div>PCB数量</div><div>{{ data?.samsungindex. pcbcount }}</div>
        <div>错误PCB数量</div><div>{{ data?.samsungindex. errorpcbcount }}</div>
        <div>跳过的PCB数量</div><div>{{ data?.samsungindex. skippcbcount }}</div>
        <div>运行率</div><div>{{ data?.samsungindex. operationrate }}</div>
        <div>放置率</div><div>{{ data?.samsungindex. placementrate }}</div>
        <div>单个PCB的平均时间</div><div>{{ data?.samsungindex. meantimeperpcb }}</div>
        <div>实际每个PCB的时间</div><div>{{ data?.samsungindex. realtimeperpcb }}</div>
        <div>每个PCB的转移时间</div><div>{{ data?.samsungindex. transfertimeperpcb }}</div>
        <div>放置数量</div><div>{{ data?.samsungindex. placecount }}</div>
      </div>
    </Panel>
    <Panel header="吸取数（吸嘴）" class="mb-4 w-full">
      <DataTable :value="data?.samsunglogheader" :rows="10" scrollable paginator scroll-height="800px">
        <Column header="贴片头编号" field="head1" />
        <Column header="备注" field="head2" />
        <Column header="拾取次数" field="head3" />
        <Column header="错误次数" field="head4" />
        <Column header="成功次数" field="head5" />
        <Column header="跳过次数" field="head6" />
        <Column header="漏拾次数" field="head7" />
        <Column header="错误拾取次数" field="head8" />
      </DataTable>
    </Panel>
    <Panel header="tapefeeder" class="mb-4 w-full">
      <DataTable :value="data?.tapefeeder" :rows="10" scrollable paginator scroll-height="800px">
        <Column header="位置编号" field="position" />
        <Column header="送料器ID" field="id" />
        <Column header="料号" field="materialnum" />
        <Column header="拾取数量" field="pickcount" />
        <Column header="错误数量" field="errorcount" />
        <Column header="成功数量" field="successcount" />
        <Column header="跳过数量" field="skipcount" />
        <Column header="漏拾数量" field="misscount" />
        <Column header="错误拾取数量" field="errorpickcount" />
        <Column header="位置信息" field="location" />
      </DataTable>
    </Panel>
    <Panel header="stickerfeeder" class="mb-4 w-full">
      <DataTable :value="data?.stickerfeeder" :rows="10" scrollable paginator scroll-height="800px">
        <Column header="送料器ID" field="id" />
        <Column header="料号" field="materialnum" />
        <Column header="拾取数量" field="pickcount" />
        <Column header="错误数量" field="errorcount" />
        <Column header="成功数量" field="successcount" />
        <Column header="跳过数量" field="skipcount" />
        <Column header="漏拾数量" field="misscount" />
        <Column header="错误拾取数量" field="errorpickcount" />
        <Column header="位置信息" field="location" />
      </DataTable>
    </Panel>
    <Panel header="trayfeeder" class="mb-4 w-full">
      <DataTable :value="data?.trayfeeder" :rows="10" scrollable paginator scroll-height="800px">
        <Column header="送料器ID" field="id" />
        <Column header="料号" field="materialnum" />
        <Column header="拾取数量" field="pickcount" />
        <Column header="错误数量" field="errorcount" />
        <Column header="成功数量" field="successcount" />
        <Column header="跳过数量" field="skipcount" />
        <Column header="漏拾数量" field="misscount" />
        <Column header="错误拾取数量" field="errorpickcount" />
        <Column header="位置信息" field="location" />
      </DataTable>
    </Panel>
    <Panel header="吸嘴" class="mb-4 w-full">
      <DataTable :value="data?.nozzle" :rows="10" scrollable paginator scroll-height="800px">
        <Column header="喷嘴编号" field="nozzlenum" />
        <Column header="喷嘴型号" field="nozzlechanger" />
        <Column header="拾取数量" field="pickcount" />
        <Column header="错误数量" field="errorcount" />
        <Column header="成功数量" field="successcount" />
        <Column header="跳过数量" field="skipcount" />
        <Column header="漏拾数量" field="misscount" />
        <Column header="错误拾取数量" field="errorpickcount" />
        <Column header="位置信息" field="location" />
      </DataTable>
    </Panel>
  </div>
</template>
