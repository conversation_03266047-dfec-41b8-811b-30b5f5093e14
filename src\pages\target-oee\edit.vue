<script setup lang="ts">
import { useTargetOeeEditForm } from './schema'
import type { TargetOee } from '~/api/target-oee/type'
import { targetOeeApi } from '~/api/target-oee'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit, setValues } = useTargetOeeEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await targetOeeApi.update(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const targetOee: TargetOee = await targetOeeApi.get(props.id)
    setValues(targetOee)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="编辑目标OEE" @show="loadData">
    <form @submit="save">
      <FormLayout>
        <LInputNumber name="availability" label="稼动率" />
        <LInputNumber name="performance" label="运行效率" />
        <LInputNumber name="quality" label="良品率" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
