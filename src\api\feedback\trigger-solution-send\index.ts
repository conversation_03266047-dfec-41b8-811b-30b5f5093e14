import type {
  FeedbackTriggerSolutionSend,
  FeedbackTriggerSolutionSendCreateParam,
  FeedbackTriggerSolutionSendSearchParam,
  FeedbackTriggerSolutionSendUpdateParam,
  FeedbackTriggerSolutionSendWithId,
} from './type'
import type { PageList, Pageable } from '~/api/common/type'
import { kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const TriggerSolutionSendApi = {
  /**
   * 创建快返触发解决方案发送记录
   */
  create: (param: FeedbackTriggerSolutionSendCreateParam) =>
    kyPost('feedback-trigger-solution-send', param).json<FeedbackTriggerSolutionSendWithId>(),

  /**
   * 分页查询快返触发解决方案发送记录
   */
  page: (param: Pageable<FeedbackTriggerSolutionSendSearchParam>) =>
    kyPost('feedback-trigger-solution-send/page', param).json<PageList<FeedbackTriggerSolutionSendWithId>>(),

  /**
   * 根据ID获取快返触发解决方案发送记录
   */
  findById: (id: string) =>
    kyGet(`feedback-trigger-solution-send/${id}`).json<FeedbackTriggerSolutionSend>(),

  /**
   * 更新快返触发解决方案发送记录
   */
  update: (id: string, param: FeedbackTriggerSolutionSendUpdateParam) =>
    kyPut(`feedback-trigger-solution-send/${id}`, param).json<FeedbackTriggerSolutionSendWithId>(),

  /**
   * 删除快返触发解决方案发送记录
   */
  delete: (id: string) =>
    kyDelete(`feedback-trigger-solution-send/${id}`),

  /**
   * 批量删除快返触发解决方案发送记录
   */
  batchDelete: (ids: string[]) =>
    kyPost('feedback-trigger-solution-send/batch-delete', { ids }),

  /**
   * 根据触发记录ID获取解决方案发送记录列表
   */
  findByTriggerRecordId: (triggerRecordId: string) =>
    kyGet(`feedback-trigger-solution-send/by-trigger-record/${triggerRecordId}`).json<FeedbackTriggerSolutionSendWithId[]>(),

  /**
   * 根据解决方案ID获取发送记录列表
   */
  findByTriggerSolutionId: (triggerSolutionId: string) =>
    kyGet(`feedback-trigger-solution-send/by-trigger-solution/${triggerSolutionId}`).json<FeedbackTriggerSolutionSendWithId[]>(),

  /**
   * 手动发送
   */
  send: (id: string) =>
    kyPost(`feedback-trigger-solution-send/${id}/perform-send`).text(),

  /**
   * 批量发送
   */
  batchSend: (ids: string[]) =>
    kyPost('feedback-trigger-solution-send/batch-send', { ids }),
}
