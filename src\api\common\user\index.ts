import type { PageList, Pageable } from '../type'
import type { UserCreate, UserQuery, UserUpdate, UserView } from './types'
import { kyDelete, kyGet, kyPost } from '~/utils/request'

export const userApi = {
  create: (param: UserCreate) => kyPost('user/create', param),
  update: (id: string, param: UserUpdate) => kyPost(`user/update/${id}`, param),
  del: (id: string) => kyDelete(`user/${id}`),
  page: (param: Pageable<UserQuery>) => kyPost('user/page', param).json <PageList<UserView>>(),
  get: (id: string) => kyGet(`user/${id}`).json<UserView>(),
  changePassword: (oldPassword: string, newPassword: string) =>
    kyPost('user/changePassword', { oldPassword, newPassword }),
}
