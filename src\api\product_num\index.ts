import type { PageList, Pageable } from '../common/type'
import type { Product, ProductCreateParam } from './types'
import { kyDelete, kyGet, kyPost, kyPostFile, kyPut } from '~/utils/request'

export const productApi = {
  create: (param: ProductCreateParam) => kyPost(`product`, param),
  page: (param: Pageable<Partial<Product>>) => kyPost(`product/page`, param).json<PageList<Product>>(),
  delete: (id: string) => kyDelete(`product/${id}`),
  get: (id: string) => kyGet(`product/${id}`).json<Product>(),
  update: (id: string, param: ProductCreateParam) => kyPut(`product/${id}`, param),
  export: (param: Partial<Product>) => kyPost(`product/export`, param),
  import: (formData: FormData) => kyPostFile(`product/import`, formData),
}
