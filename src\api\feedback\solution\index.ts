import type { FeedbackTriggerSolution, FeedbackTriggerSolutionCreateParam, FeedbackTriggerUpdateParam, SolutionRejectRequest } from './type'
import type { PageList, Pageable } from '~/api/common/type'
import { kyGet, kyPost, kyPut } from '~/utils/request'

export const SolutionApi = {
  getByTriggerRecordAndSend: (triggerRecordId: string, triggerSendId: string) =>
    kyGet('feedback-trigger-solutions/by-trigger-record-and-send', { triggerRecordId, triggerSendId }).json<FeedbackTriggerSolution>(),
  create: (param: FeedbackTriggerSolutionCreateParam) => kyPost('feedback-trigger-solutions', param),
  update: (id: string, param: Partial<FeedbackTriggerUpdateParam>) => kyPut(`feedback-trigger-solutions/${id}`, param),
  submit: (id: string) => kyPut(`feedback-trigger-solutions/${id}/submit`).json<FeedbackTriggerSolution>(),
  reject: (id: string, request: SolutionRejectRequest) => kyPut(`feedback-trigger-solutions/${id}/reject`, request).json<FeedbackTriggerSolution>(),
  page: (param: Pageable<Partial<FeedbackTriggerSolution>>) =>
    kyPost('feedback-trigger-solutions/page', param).json<PageList<FeedbackTriggerSolution>>(),
}

// URL解密相关API
export interface UrlDecryptResponse {
  success: boolean
  message: string
  data: Record<string, string>
}

export interface UrlEncryptionStatusResponse {
  success: boolean
  message: string
  data: {
    enabled: boolean
    description: string
  }
}

export const UrlDecryptionApi = {
  /**
   * 解密URL查询参数
   * @param data 加密的参数数据
   * @returns 解密后的参数Map
   */
  decrypt: (data: string) => kyGet('url/decrypt', { data }).json<UrlDecryptResponse>(),

  /**
   * 检查URL加密状态
   * @returns 加密状态信息
   */
  getEncryptionStatus: () => kyGet('url/encryption-status').json<UrlEncryptionStatusResponse>(),
}
