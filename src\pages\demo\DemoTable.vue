<script setup lang="ts">
import { type DataTablePageEvent, useConfirm } from 'primevue'
import { formatDate } from '@vueuse/core'
import type { DemoDataWithId } from './schema'
import { demoOptions, demoSearchSchema, useDemoData } from './schema'
import DemoCreate from './DemoCreate.vue'
import DemoEdit from './DemoEdit.vue'
import PageContainer from '~/components/common/PageContainer.vue'
import type { PageData } from '~/api/common/type'

const open = ref({
  create: false,
  edit: false,
})
const editId = ref<string>()

const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 1,
})

const total = ref(0)

const loading = ref<boolean>(false)

const data = ref<DemoDataWithId[]>([])

const { fetchDemoData, del } = await useDemoData()

const confirm = useConfirm()
const searchForm = useForm({
  validationSchema: demoSearchSchema,
})

const search = searchForm.handleSubmit(async (values) => {
  try {
    loading.value = true
    const res = await fetchDemoData(values, pageData)
    data.value = res.data
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

function openEditPage(id: string) {
  editId.value = id
  open.value.edit = true
}

function openCreatePage() {
  open.value.create = true
}

function confirmDel(id: string, event: any) {
  confirm.require({
    target: event.currentTarget,
    message: '确认删除？',
    acceptLabel: '是',
    rejectLabel: '否',
    rejectClass: 'outline',
    accept: async () => {
      await del(id)
      success('删除成功')
      data.value = data.value.filter(o => o.id !== id)
    },
  })
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer>
    <SearchBox :loading="loading" @submit="search">
      <FInput name="name" label="名称" />
      <FSelect name="option1" label="选项1" :options="demoOptions" />
      <FDatePicker name="date" label="日期" date-props.format="yy-MM-dd" />
    </SearchBox>
    <div class="pl-4">
      <Button outlined @click="openCreatePage">
        新增
      </Button>
    </div>
    <DataTable class="mt-4 p-4" :value="data" lazy paginator data-key="id" :rows="pageData.pageSize" :total-records="total" @page="page">
      <Column field="id" header="ID" />
      <Column field="name" header="名称" />
      <Column header="描述">
        <template #body="slotProps">
          <Textarea readonly :value="slotProps.data.description" />
        </template>
      </Column>
      <Column header="选项1">
        <template #body="slotProps">
          {{ demoOptions.find(o => o.value === slotProps.data.option1)?.label }}
        </template>
      </Column>
      <Column header="是否">
        <template #body="slotProps">
          {{ slotProps.data.yesno ? '是' : '否' }}
        </template>
      </Column>
      <Column header="日期">
        <template #body="slotProps">
          {{ formatDate(slotProps.data.date1, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
      </Column>
      <Column header="时间">
        <template #body="slotProps">
          {{ formatDate(slotProps.data.time, 'HH:mm:ss') }}
        </template>
      </Column>

      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined @click="openEditPage(slotProps.data.id)">
              编辑
            </Button>
            <Button outlined severity="danger" @click="confirmDel(slotProps.data.id, $event)">
              删除
            </Button>
          </div>
        </template>
      </Column>
    </DataTable>
    <DemoCreate v-model:open="open.create" @save="search" />
    <DemoEdit :id="editId" v-model:open="open.edit" @save="search" />
  </PageContainer>
</template>
