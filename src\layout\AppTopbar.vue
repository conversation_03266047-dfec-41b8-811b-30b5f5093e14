<script setup>
import AppConfigurator from './AppConfigurator.vue'
import UserPop from './UserPop.vue'
import { useLayoutStore } from '~/stores/layout'
import { useMenuStore } from '~/stores/menu'

const { toggleDarkMode } = useLayoutStore()
const { onMenuToggle } = useMenuStore()
</script>

<template>
  <div class="layout-topbar">
    <div class="layout-topbar-logo-container">
      <button class="layout-menu-button layout-topbar-action" @click="onMenuToggle">
        <i class="pi pi-bars" />
      </button>
      <router-link to="/" class="layout-topbar-logo">
        <span>后台管理</span>
      </router-link>
    </div>

    <div class="layout-topbar-actions">
      <div class="layout-config-menu">
        <button type="button" class="layout-topbar-action" @click="toggleDarkMode">
          <i class="pi" :class="isDark ? 'pi-moon' : 'pi-sun'" />
        </button>
        <div class="relative">
          <button
            v-styleclass="{ selector: '@next', enterFromClass: 'hidden', enterActiveClass: 'animate-scale-in animate-duration-100', leaveToClass: 'hidden', leaveActiveClass: 'animate-fade-out animate-duration-100', hideOnOutsideClick: true }"
            type="button"
            class="layout-topbar-action layout-topbar-action-highlight"
          >
            <i class="pi pi-palette" />
          </button>
          <AppConfigurator />
        </div>
      </div>

      <button
        v-styleclass="{ selector: '@next', enterFromClass: 'hidden', enterActiveClass: 'animate-scale-in animate-duration-100', leaveToClass: 'hidden', leaveActiveClass: 'animate-fade-out animate-duration-100', hideOnOutsideClick: true }"
        class="layout-topbar-action layout-topbar-menu-button"
      >
        <i class="pi pi-ellipsis-v" />
      </button>

      <div class="layout-topbar-menu hidden lg:block">
        <div class="layout-topbar-menu-content">
          <UserPop @change-password="showPasswordDialog" />
        </div>
      </div>
    </div>
  </div>
</template>
