<script setup lang="ts">
import { useDeviceEditForm } from './schema'
import type { Device } from '~/api/device/types'
import { deviceApi } from '~/api/device'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit, setValues } = useDeviceEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await deviceApi.update(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const device: Device = await deviceApi.get(props.id)
    setValues(device)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="编辑设备" @show="loadData">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="设备名称" />
        <LInput name="code" label="设备编码" />
        <LSelect name="type" label="设备类型" :options="deviceTypeOptions" />
        <LSelect name="category" label="设备类别" :options="deviceCategoryOptions" />
        <LInput name="track" label="轨道" />
        <LSelect name="lineId" label="产线" :options="lineOptions" />
        <LInput name="clientIp" label="客户端IP" />
        <LInput name="clientPort" label="客户端端口" />
        <LInputNumber name="sort" label="排序" />
        <LInputNumber name="group" label="分组" />
        <LSelect name="enable" label="状态" :options="enableOptions" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
  </Dialog>
</template>
