<script setup lang="ts">
import { useDark } from '@vueuse/core'
import type { AlarmInfo } from '~/api/analyze/type'
import { responsivePx } from '~/utils/responsive'

const props = withDefaults(defineProps<{
  data?: AlarmInfo[]
}>(), {
  data: () => [],
})

const isDark = useDark()

const baseOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' },
  },
  grid: {
    left: '3%',
    right: '8%',
    bottom: '3%',
    top: '15%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    name: '时长',
    nameLocation: 'end',
    axisLine: { show: false },
    axisTick: { show: false },
  },
  yAxis: {
    type: 'category',
    axisTick: { show: false },
  },
  series: [{
    name: '异常时长',
    type: 'bar',
    barWidth: '60%',
    itemStyle: {
      borderRadius: [0, 4, 4, 0],
    },
    label: {
      show: true,
      position: 'right',
      fontSize: responsivePx(12),
    },
  }],
}

const chartOption = computed(() => {
  const hasData = props.data.length > 0
  const textColor = isDark.value ? '#E5EAF3' : '#666'

  const names = props.data.map(item => item.name).reverse()
  const values = props.data.map(item => item.num).reverse()

  return {
    ...baseOption,
    legend: {
      data: ['异常时长'],
      textStyle: {
        color: textColor,
        fontSize: responsivePx(12),
      },
      itemWidth: responsivePx(12),
      itemHeight: responsivePx(6),
    },
    xAxis: {
      ...baseOption.xAxis,
      nameTextStyle: {
        color: textColor,
        fontSize: responsivePx(12),
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: isDark.value ? 'rgba(84,89,104,0.3)' : '#DDD',
        },
      },
      axisLabel: {
        color: textColor,
        fontSize: responsivePx(12),
      },
    },
    yAxis: {
      ...baseOption.yAxis,
      data: names,
      axisLabel: {
        color: textColor,
        fontSize: responsivePx(12),
      },
      axisLine: {
        lineStyle: {
          color: isDark.value ? '#626675' : '#999',
        },
      },
    },
    series: [{
      ...baseOption.series[0],
      data: values,
      label: {
        ...baseOption.series[0].label,
        color: textColor,
      },
      itemStyle: {
        ...baseOption.series[0].itemStyle,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: '#FFC107' },
            { offset: 1, color: isDark.value ? '#B28704' : '#FFE082' },
          ],
        },
      },
    }],
    graphic: !hasData
      ? [{
          type: 'text',
          left: 'center',
          top: 'middle',
          style: { text: '暂无数据', fontSize: responsivePx(12), fill: textColor },
        }]
      : undefined,
  }
})
</script>

<template>
  <e-charts :option="chartOption" autoresize />
</template>
