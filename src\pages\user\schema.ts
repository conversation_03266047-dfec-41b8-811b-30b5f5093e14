import { toTypedSchema } from '@vee-validate/zod'

export const userQuerySchema = toTypedSchema(
  z.object(
    {
      name: z.string().optional(),
      displayName: z.string().optional(),
    },
  ),
)

export const userCreateSchema = toTypedSchema(
  z.object({
    name: z.string(),
    displayName: z.string(),
    avatar: z.string().optional(),
    password: z.string().min(6),
  }),
)

export const userUpdateSchema = toTypedSchema(
  z.object({
    displayName: z.string(),
    avatar: z.string().optional(),
    roles: z.array(z.string()),
    perms: z.array(z.string()),
  }),
)

export function useUserQueryForm() {
  return useForm({
    validationSchema: userQuerySchema,
  })
}

export function useUserUpdateForm() {
  return useForm({
    validationSchema: userUpdateSchema,
  })
}

export function useUserCreateForm() {
  return useForm({
    validationSchema: userCreateSchema,
  })
}
