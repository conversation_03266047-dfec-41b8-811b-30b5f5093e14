import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'

// 查询理论值
const TheoreticalOutputSearchSchema = toTypedSchema(z.object({
  lineCode: z.string().optional(),
  deviceCode: z.string().optional(),
  deviceType: z.string().optional(),
  productModel: z.string().optional(),
  hourlyOutput: z.number().optional(),
  ct: z.number().optional(),
}))

// 创建理论值
const TheoreticalOutputCreateSchema = toTypedSchema(z.object({
  lineCode: z.string({
    required_error: '请输入线体编码',
  }),
  deviceCode: z.string({
    required_error: '请输入设备编码',
  }),
  deviceType: z.string({
    required_error: '请输入设备类型',
  }),
  productModel: z.string({
    required_error: '请输入产品型号',
  }),
  hourlyOutput: z.number().optional(),
  ct: z.number({
    required_error: '请输入理论CT',
    invalid_type_error: '请输入有效的数字',
  }),
  flatNumber: z.number({
    required_error: '请输入拼板数',
    invalid_type_error: '请输入有效的数字',
  }),
}))

// 编辑理论值
const TheoreticalOutputEditSchema = toTypedSchema(z.object({
  id: z.string(),
  lineCode: z.string({
    required_error: '请输入线体编码',
  }),
  deviceCode: z.string({
    required_error: '请输入设备编码',
  }),
  deviceType: z.string({
    required_error: '请输入设备类型',
  }),
  productModel: z.string({
    required_error: '请输入产品型号',
  }),
  hourlyOutput: z.number().optional(),
  ct: z.number({
    required_error: '请输入理论CT',
    invalid_type_error: '请输入有效的数字',
  }),
  flatNumber: z.number({
    required_error: '请输入拼板数',
    invalid_type_error: '请输入有效的数字',
  }),
}))

export function useTheoreticalSearchForm() {
  const form = useForm({
    validationSchema: TheoreticalOutputSearchSchema,
  })
  return form
}

export function useTheoreticalCreateForm() {
  const form = useForm({
    validationSchema: TheoreticalOutputCreateSchema,
  })
  return form
}

export function useTheoreticalEditForm() {
  const form = useForm({
    validationSchema: TheoreticalOutputEditSchema,
  })
  return form
}
