import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'

// 创建目标OEE
const TargetOeeCreateSchema = toTypedSchema(z.object({
  availability: z.number({
    required_error: '请输入可用性',
    invalid_type_error: '请输入有效的数字',
  }),
  performance: z.number({
    required_error: '请输入性能',
    invalid_type_error: '请输入有效的数字',
  }),
  quality: z.number({
    required_error: '请输入质量',
    invalid_type_error: '请输入有效的数字',
  }),
}))

export function useTargetOeeCreateForm() {
  const form = useForm({
    validationSchema: TargetOeeCreateSchema,
  })
  return form
}

export function useTargetOeeEditForm() {
  const form = useForm({
    validationSchema: TargetOeeCreateSchema,
  })
  return form
}
