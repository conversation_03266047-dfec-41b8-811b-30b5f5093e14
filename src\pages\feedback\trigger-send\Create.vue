<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { ref } from 'vue'
import UserSelectDialog from '../config/UserSelectDialog.vue'
import { useFeedbackTriggerSendCreateForm } from './schema'
import { TriggerSendApi } from '~/api/feedback/trigger-send'
import { SEND_STATUS_OPTIONS } from '~/api/feedback/trigger-send/type'
import type { User } from '~/api/wx-server/types'
import { error, success } from '~/composables/toast'

const emits = defineEmits<{ save: [] }>()
const loading = ref(false)
const open = defineModel<boolean>('open')

// 用户选择相关状态
const sendUserSelectOpen = ref(false)
const reportUserSelectOpen = ref(false)
const selectedSendUsers = ref<User[]>([])
const selectedReportUsers = ref<User[]>([])

const { handleSubmit, resetForm, setFieldValue } = useFeedbackTriggerSendCreateForm()
const { errorMessage: sendUserError } = useField('sendUserId')
const { errorMessage: reportUserError } = useField('reportUserId')

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await TriggerSendApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  catch (err) {
    console.error('创建失败:', err)
    error('创建失败')
  }
  finally {
    loading.value = false
  }
})

// 响应人选择处理
function handleSendUserSelected(users: User[]) {
  selectedSendUsers.value = users
  setFieldValue('sendUserId', users.map(user => user.userid))
}

// 告知人选择处理
function handleReportUserSelected(users: User[]) {
  selectedReportUsers.value = users
  setFieldValue('reportUserId', users.map(user => user.userid))
}

// 移除选中的响应人
function removeSendUser(index: number) {
  selectedSendUsers.value.splice(index, 1)
  setFieldValue('sendUserId', selectedSendUsers.value.map(user => user.userid))
}

// 移除选中的告知人
function removeReportUser(index: number) {
  selectedReportUsers.value.splice(index, 1)
  setFieldValue('reportUserId', selectedReportUsers.value.map(user => user.userid))
}

// 打开响应人选择对话框
function openSendUserSelect() {
  sendUserSelectOpen.value = true
}

// 打开告知人选择对话框
function openReportUserSelect() {
  reportUserSelectOpen.value = true
}

// 获取当前选中的响应人（用于传递给UserSelectDialog）
const currentSelectedSendUsers = computed(() => {
  return selectedSendUsers.value.map(user => ({
    userid: user.userid,
    name: user.name || '',
    department: user.department || [],
  }))
})

// 获取当前选中的告知人（用于传递给UserSelectDialog）
const currentSelectedReportUsers = computed(() => {
  return selectedReportUsers.value.map(user => ({
    userid: user.userid,
    name: user.name || '',
    department: user.department || [],
  }))
})

function initializeForm() {
  resetForm()
  selectedSendUsers.value = []
  selectedReportUsers.value = []
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建快返触发发送记录" :style="{ width: '50rem' }" @show="initializeForm">
    <form @submit.prevent="save">
      <div class="space-y-6">
        <LInput name="triggerRecordId" label="触发记录ID" />
        <LDatePicker name="expectedSendTime" label="预期发送时间" :date-props="{ showTime: true, showSeconds: true }" />

        <!-- 响应人选择 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">
            响应人 <span class="text-red-500">*</span>
          </label>
          <div class="flex items-center gap-2">
            <Button type="button" icon="pi pi-user-plus" label="选择响应人" outlined @click="openSendUserSelect" />
            <span class="text-sm text-muted-color">已选择 {{ selectedSendUsers.length }} 人</span>
          </div>

          <!-- 已选响应人显示区域 -->
          <div v-if="selectedSendUsers.length > 0" class="h-32 overflow-auto border-1 rounded-md p-2 border-surface">
            <div class="space-y-1">
              <div v-for="(user, index) in selectedSendUsers" :key="user.userid">
                <Chip
                  :label="user.name || user.userid" class="w-full justify-between" removable
                  @remove="removeSendUser(index)"
                >
                  <span class="text-sm">{{ user.name || user.userid }}</span>
                </Chip>
              </div>
            </div>
          </div>
          <div v-else class="h-32 flex items-center justify-center border-1 rounded-md border-dashed border-surface">
            <span class="text-muted-color">请选择响应人</span>
          </div>
          <ErrorMsg :error-message="sendUserError" />
        </div>

        <!-- 告知人选择 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">
            告知人 <span class="text-red-500">*</span>
          </label>
          <div class="flex items-center gap-2">
            <Button type="button" icon="pi pi-user-plus" label="选择告知人" outlined @click="openReportUserSelect" />
            <span class="text-sm text-muted-color">已选择 {{ selectedReportUsers.length }} 人</span>
          </div>

          <!-- 已选告知人显示区域 -->
          <div v-if="selectedReportUsers.length > 0" class="h-32 overflow-auto border-1 rounded-md p-2 border-surface">
            <div class="space-y-1">
              <div v-for="(user, index) in selectedReportUsers" :key="user.userid">
                <Chip
                  :label="user.name || user.userid" class="w-full justify-between" removable
                  @remove="removeReportUser(index)"
                >
                  <span class="text-sm">{{ user.name || user.userid }}</span>
                </Chip>
              </div>
            </div>
          </div>
          <div v-else class="h-32 flex items-center justify-center border-1 rounded-md border-dashed border-surface">
            <span class="text-muted-color">请选择告知人</span>
          </div>
          <ErrorMsg :error-message="reportUserError" />
        </div>

        <LTextarea name="sendInfo" label="发送信息" />
        <LSelect name="sendStatus" label="发送状态" :options="SEND_STATUS_OPTIONS" />
      </div>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" label="创建" icon="pi pi-check" />
        <Button type="button" label="重置" icon="pi pi-refresh" severity="secondary" @click="resetForm()" />
      </div>
    </form>
  </Dialog>

  <!-- 响应人选择对话框 -->
  <UserSelectDialog
    v-model:open="sendUserSelectOpen"
    :initial-selected-users="currentSelectedSendUsers"
    @select="handleSendUserSelected"
  />

  <!-- 告知人选择对话框 -->
  <UserSelectDialog
    v-model:open="reportUserSelectOpen"
    :initial-selected-users="currentSelectedReportUsers"
    @select="handleReportUserSelected"
  />
</template>
