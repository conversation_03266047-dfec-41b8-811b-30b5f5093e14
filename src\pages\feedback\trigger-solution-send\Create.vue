<script setup lang="ts">
import Button from 'primevue/button'
import Chip from 'primevue/chip'
import Dialog from 'primevue/dialog'
import { computed, ref } from 'vue'
import { useField } from 'vee-validate'
import UserSelectDialog from '../config/UserSelectDialog.vue'
import { useFeedbackTriggerSolutionSendCreateForm } from './schema'
import { TriggerSolutionSendApi } from '~/api/feedback/trigger-solution-send'
import type { User } from '~/api/wx-server/types'
import { error, success } from '~/composables/toast'

const emits = defineEmits<{ save: [] }>()
const loading = ref(false)
const open = defineModel<boolean>('open')

// 用户选择相关状态
const sendUserSelectOpen = ref(false)
const reportUserSelectOpen = ref(false)
const selectedSendUsers = ref<User[]>([])
const selectedReportUsers = ref<User[]>([])

const { handleSubmit, resetForm, setFieldValue } = useFeedbackTriggerSolutionSendCreateForm()
const { errorMessage: sendUserError } = useField('sendUserId')
const { errorMessage: reportUserError } = useField('reportUserId')

// 消息类型选项
const messageTypeOptions = [
  { label: '解决方案通知', value: 'SOLUTION_NOTIFICATION' },
  { label: '退回通知', value: 'REJECT_NOTIFICATION' },
]

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await TriggerSolutionSendApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  catch (err) {
    console.error('创建失败:', err)
    error('创建失败')
  }
  finally {
    loading.value = false
  }
})

// 发送人选择处理
function handleSendUserSelected(users: User[]) {
  selectedSendUsers.value = users
  setFieldValue('sendUserId', users.map(user => user.userid))
}

// 告知人选择处理
function handleReportUserSelected(users: User[]) {
  selectedReportUsers.value = users
  setFieldValue('reportUserId', users.map(user => user.userid))
}

// 移除选中的发送人
function removeSendUser(index: number) {
  selectedSendUsers.value.splice(index, 1)
  setFieldValue('sendUserId', selectedSendUsers.value.map(user => user.userid))
}

// 移除选中的告知人
function removeReportUser(index: number) {
  selectedReportUsers.value.splice(index, 1)
  setFieldValue('reportUserId', selectedReportUsers.value.map(user => user.userid))
}

// 打开发送人选择对话框
function openSendUserSelect() {
  sendUserSelectOpen.value = true
}

// 打开告知人选择对话框
function openReportUserSelect() {
  reportUserSelectOpen.value = true
}

// 当前选中的发送人（用于传递给对话框）
const currentSelectedSendUsers = computed(() => selectedSendUsers.value)

// 当前选中的告知人（用于传递给对话框）
const currentSelectedReportUsers = computed(() => selectedReportUsers.value)

// 初始化表单
function initializeForm() {
  selectedSendUsers.value = []
  selectedReportUsers.value = []
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建快返触发解决方案发送记录" :style="{ width: '50rem' }" @show="initializeForm">
    <form @submit.prevent="save">
      <div class="space-y-6">
        <LInput name="triggerSolutionId" label="解决方案ID" />
        <LInput name="triggerRecordId" label="触发记录ID" />
        <LInput name="triggerSendId" label="发送记录ID" />
        <LDatePicker name="expectedSendTime" label="预期发送时间" :date-props="{ showTime: true, showSeconds: true }" />

        <!-- 发送人选择 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">
            发送人 <span class="text-red-500">*</span>
          </label>
          <div class="flex items-center gap-2">
            <Button type="button" icon="pi pi-user-plus" label="选择发送人" outlined @click="openSendUserSelect" />
            <span class="text-sm text-muted-color">已选择 {{ selectedSendUsers.length }} 人</span>
          </div>

          <!-- 显示选中的发送人 -->
          <div v-if="selectedSendUsers.length > 0" class="max-h-32 overflow-y-auto border border-surface-300 rounded-md p-3">
            <div class="flex flex-wrap gap-2">
              <Chip
                v-for="(user, index) in selectedSendUsers"
                :key="user.userid"
                :label="user.name"
                removable
                @remove="removeSendUser(index)"
              />
            </div>
          </div>

          <!-- 错误信息 -->
          <div v-if="sendUserError" class="text-sm text-red-500">
            {{ sendUserError }}
          </div>
        </div>

        <!-- 告知人选择 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">
            告知人 <span class="text-red-500">*</span>
          </label>
          <div class="flex items-center gap-2">
            <Button type="button" icon="pi pi-user-plus" label="选择告知人" outlined @click="openReportUserSelect" />
            <span class="text-sm text-muted-color">已选择 {{ selectedReportUsers.length }} 人</span>
          </div>

          <!-- 显示选中的告知人 -->
          <div v-if="selectedReportUsers.length > 0" class="max-h-32 overflow-y-auto border border-surface-300 rounded-md p-3">
            <div class="flex flex-wrap gap-2">
              <Chip
                v-for="(user, index) in selectedReportUsers"
                :key="user.userid"
                :label="user.name"
                removable
                @remove="removeReportUser(index)"
              />
            </div>
          </div>

          <!-- 错误信息 -->
          <div v-if="reportUserError" class="text-sm text-red-500">
            {{ reportUserError }}
          </div>
        </div>

        <LSelect name="messageType" label="消息类型" :options="messageTypeOptions" />
        <LTextarea name="sendInfo" label="发送信息" />
        <LBoolRadioGroup name="sendStatus" label="发送状态" />
      </div>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" label="创建" icon="pi pi-check" />
        <Button type="button" label="重置" icon="pi pi-refresh" severity="secondary" @click="resetForm()" />
      </div>
    </form>
  </Dialog>

  <!-- 发送人选择对话框 -->
  <UserSelectDialog
    v-model:open="sendUserSelectOpen"
    :initial-selected-users="currentSelectedSendUsers"
    @select="handleSendUserSelected"
  />

  <!-- 告知人选择对话框 -->
  <UserSelectDialog
    v-model:open="reportUserSelectOpen"
    :initial-selected-users="currentSelectedReportUsers"
    @select="handleReportUserSelected"
  />
</template>
