import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'

// 查询理论值
const ProductSearchSchema = toTypedSchema(z.object({
  lineCode: z.string().optional(),
  productModel: z.string().optional(),
  pointNum: z.number().optional(),
}))

// 创建理论值
const ProductCreateSchema = toTypedSchema(z.object({
  lineCode: z.string({
    required_error: '请输入线体编码',
  }).optional(),
  productModel: z.string({
    required_error: '请输入产品型号',
  }),
  pointNum: z.number({
    required_error: '请输入点数',
    invalid_type_error: '请输入有效的数字',
  }),
}))

// 编辑理论值
const ProductEditSchema = toTypedSchema(z.object({
  lineCode: z.string({
    required_error: '请输入线体编码',
  }).optional(),
  productModel: z.string({
    required_error: '请输入产品型号',
  }),

  pointNum: z.number({
    required_error: '请输入点数',
    invalid_type_error: '请输入有效的数字',
  }),
}))

export function useProductSearchForm() {
  const form = useForm({
    validationSchema: ProductSearchSchema,
  })
  return form
}

export function useProductCreateForm() {
  const form = useForm({
    validationSchema: ProductCreateSchema,
  })
  return form
}

export function useProductEditForm() {
  const form = useForm({
    validationSchema: ProductEditSchema,
  })
  return form
}
