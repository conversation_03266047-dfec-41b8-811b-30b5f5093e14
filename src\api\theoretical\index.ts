import type { PageList, Pageable } from '../common/type'
import type { TheoreticalOutput, TheoreticalOutputCreateParam } from './types'
import { kyBatchDelete, kyDelete, kyGet, kyPost, kyPostFile, kyPut } from '~/utils/request'

export const theoreticalApi = {
  create: (param: TheoreticalOutputCreateParam) => kyPost('theoretical-output', param),
  page: (param: Pageable<Partial<TheoreticalOutput>>) => kyPost('theoretical-output/page', param).json<PageList<TheoreticalOutput>>(),
  delete: (id: string) => kyDelete(`theoretical-output/${id}`),
  batchDelete: (ids: string[]) => kyBatchDelete('theoretical-output/batch', { json: ids }),
  get: (id: string) => kyGet(`theoretical-output/${id}`).json<TheoreticalOutput>(),
  update: (id: string, param: TheoreticalOutputCreateParam) => kyPut(`theoretical-output/${id}`, param),
  export: (param: Partial<TheoreticalOutput>) => kyPost('theoretical-output/export', param),
  import: (formData: FormData) => kyPostFile('theoretical-output/import', formData),
}
