<script setup lang="ts">
import { ref } from 'vue'
import { productApi } from '~/api/product/manage'
import type { Product } from '~/api/product/manage/type'
import { router } from '~/router'

const products = ref<Product[]>([])

// Function to handle the click event on a code
function getProducts() {
  productApi.list().then((res) => {
    products.value = res
  })
}

function onCodeClick(index: number) {
  router.push(`/admin/product/info?id=${index}`)
}

getProducts()
</script>

<template>
  <div class="card">
    <DataTable :value="products" show-gridlines table-style="min-width: 50rem">
      <Column field="date" header="时间" />
      <Column field="lane" header="轨道" />
      <Column field="filename" header="MJS 文件名" />
      <!-- Custom template for the Code column -->
      <Column header="生产信息">
        <template #body="slotProps">
          <a href="#" @click.prevent="onCodeClick(slotProps.index)">
            {{ slotProps.data.productInfo }}
          </a>
        </template>
      </Column>
    </DataTable>
  </div>
</template>
