import { toTypedSchema } from '@vee-validate/zod'
import type { PageData } from '~/api/common/type'

export enum DemoEnum {
  enum1 = 'enum1',
  enum2 = 'enum2',
  enum3 = 'enum3',
}

export const demoOptions = [
  {
    label: '选项1',
    value: 'enum1',
  },
  {
    label: '选项2',
    value: 'enum2',
  },
  {
    label: '选项3',
    value: 'enum3',
  },
]

export interface DemoItem {
  label: string
  date: Date
  option: DemoEnum
}

export interface DemoData {
  name: string
  description?: string
  date1: Date
  date2?: Date
  option1: DemoEnum
  option2?: DemoEnum
  radio: DemoEnum
  yesno: boolean
  time: Date
  items: DemoItem[]
}

export interface DemoDataSearch {
  name?: string
  date1?: Date
  option1?: DemoEnum
}

export interface DemoDataWithId extends DemoData {
  id: string
}

export const demoSchema = toTypedSchema(z.object({
  name: z.string().min(1, '不可为空'),
  description: z.string().nullish(),
  date1: z.date(),
  date2: z.date().nullish(),
  option1: z.nativeEnum(DemoEnum),
  option2: z.nativeEnum(DemoEnum).nullish(),
  radio: z.nativeEnum(DemoEnum),
  yesno: z.boolean(),
  time: z.date(),
  items: z.array(z.object({
    label: z.string(),
    date: z.date(),
    option: z.nativeEnum(DemoEnum),
  })).default([]),
}))

export const demoSearchSchema = toTypedSchema(
  z.object({
    name: z.string().optional(),
    option1: z.nativeEnum(DemoEnum).optional(),
    date1: z.date().optional(),
  }),
)

export function useDemoForm() {
  const demoForm = useForm<DemoData>({
    validationSchema: demoSchema,
  })
  return demoForm
}

export async function useDemoData() {
  function openDB() {
    return new Promise<IDBDatabase>((resolve, reject) => {
      const req = indexedDB.open('demo', 1)
      req.onupgradeneeded = (ev) => {
        const db = (ev.target as IDBOpenDBRequest).result
        if (!db.objectStoreNames.contains('demo')) {
          db.createObjectStore('demo', { keyPath: 'id', autoIncrement: true })
        }
      }

      req.onsuccess = (event) => {
        resolve((event.target as IDBOpenDBRequest).result)
      }
      req.onerror = (event) => {
        reject(event)
      }
    })
  }

  const db = await openDB()

  async function fetchDemoData(param: DemoDataSearch, pageData: PageData) {
    info(JSON.stringify(param))
    info(JSON.stringify(pageData))

    const start = pageData.pageNumber * pageData.pageSize
    const end = (pageData.pageNumber + 1) * pageData.pageSize
    return new Promise<{
      data: DemoDataWithId[]
      total: number
    }>((resolve, reject) => {
      const tx = db.transaction(['demo'], 'readwrite')
      const store = tx.objectStore('demo')
      const req = store.getAll()
      req.onsuccess = () => resolve(
        { data: req.result.slice(start, end) as DemoDataWithId[], total: req.result.length },
      )
      req.onerror = ev => reject(ev)
    })
  }

  async function get(id: string) {
    return new Promise<DemoDataWithId>((resolve, reject) => {
      const tx = db.transaction(['demo'], 'readwrite')
      const store = tx.objectStore('demo')
      const req = store.get(id)
      req.onsuccess = () => resolve(req.result as DemoDataWithId)
      req.onerror = ev => reject(ev)
    })
  }

  async function create(createParam: DemoData) {
    return new Promise<void>((resolve, reject) => {
      const tx = db.transaction(['demo'], 'readwrite')
      const store = tx.objectStore('demo')
      const req = store.add(createParam)
      req.onsuccess = () => resolve()
      req.onerror = ev => reject(ev)
    })
  }

  async function del(id: string) {
    return new Promise<void>((resolve, reject) => {
      const tx = db.transaction(['demo'], 'readwrite')
      const store = tx.objectStore('demo')
      const req = store.delete(id)
      req.onsuccess = () => resolve()
      req.onerror = ev => reject(ev)
    })
  }

  async function update(id: string, param: DemoData) {
    return new Promise<void>((resolve, reject) => {
      const tx = db.transaction(['demo'], 'readwrite')
      const store = tx.objectStore('demo')
      const req = store.put(param, id)
      req.onsuccess = () => resolve()
      req.onerror = ev => reject(ev)
    })
  }

  return { fetchDemoData, create, del, update, get }
}
