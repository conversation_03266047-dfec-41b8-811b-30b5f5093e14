/**
 * 部门信息接口
 */
export interface Department {
  /** 部门ID */
  id: number
  /** 部门名称 */
  name: string
  /** 父部门ID */
  parentid: number
  /** 排序 */
  order: number
  /** 部门负责人列表 */
  department_leader: string[]
}

export interface User {
  /** 用户ID */
  userid: string
  /** 用户姓名 */
  name: string
  /** 所属部门ID数组 */
  department: number[]
}

/**
 *  树节点接口
 */
export interface TreeNode {
  /** 节点ID */
  id: number
  /** 节点key */
  key: string
  /** 节点名称 */
  label: string
  /** 节点数据 */
  data: string
  /** 节点图标 */
  icon: string
  /** 排序 */
  order: number
  /** 子节点 */
  children: TreeNode[]
}
