<script setup lang="ts">
import GKGPrintLogView from '~/components/oee/log/GKGPrintLogView.vue'
import BlankLogView from '~/components/oee/log/BlankLogView.vue'
import { DeviceType } from '~/api/line/types'
import JutzeAoiLogView from '~/components/oee/log/JutzeAoiLogView.vue'
import NpmSMTLogView from '~/components/oee/log/NpmSMTLogView.vue'
import YamahaSMTLogView from '~/components/oee/log/YamahaSMTLogView.vue'
import SamsungLogView from '~/components/oee/log/SamsungLogView.vue'

const props = defineProps<{
  id: string
  type: DeviceType
  deviceCode: string
}>()
const logViewMap = new Map<DeviceType, Component>()
logViewMap.set(DeviceType.PRINT_GKG, GKGPrintLogView)
logViewMap.set(DeviceType.SMT_NPM, NpmSMTLogView)
logViewMap.set(DeviceType.AOI_JUZTE, JutzeAoiLogView)
logViewMap.set(DeviceType.SMT_YAMAHA, YamahaSMTLogView)
logViewMap.set(DeviceType.SMT_SAMSUNG, SamsungLogView)
const view = computed(() => {
  if (props.type) {
    if (logViewMap.has(props.type)) {
      return logViewMap.get(props.type)
    }
  }
  return BlankLogView
})
</script>

<template>
  <component
    :is="view"
    :id="id"
    :device-code="deviceCode"
    :type="type"
  />
</template>
