import type { DeviceInfo, DeviceSearchParam, ProductLine } from './types'
import { kyGet } from '~/utils/request'

export const productLineApi = {
  list: () => kyGet('line/list').json<ProductLine[]>(),
  listDevice: (param: DeviceSearchParam) => kyGet('device/list', param).json<DeviceInfo[]>(),
  listByWorkShopId: (workshopId: string) => kyGet(`line/listByWorkShopId/${workshopId}`).json<ProductLine[]>(),
}
