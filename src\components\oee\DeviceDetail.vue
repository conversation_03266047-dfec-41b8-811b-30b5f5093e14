<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue'
import { formatDate } from '@vueuse/core'

import { useRouter } from 'vue-router'
import { useLogSearchForm } from './schema'
import PageContainer from '~/components/common/PageContainer.vue'
import type { PageData } from '~/api/common/type'
import type { LogRecord, LogSearchParam } from '~/api/log/types'
import { logApi } from '~/api/log'
import type { DeviceInfo } from '~/api/line/types'

const props = defineProps < {
  deviceInfo: DeviceInfo
}>()

const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

const total = ref(0)

const loading = ref<boolean>(false)

const data = ref<LogRecord[]>([])

const searchForm = useLogSearchForm()

const router = useRouter()

const search = searchForm.handleSubmit(async (values: LogSearchParam) => {
  try {
    loading.value = true
    const res = await logApi.pageProductionLog({ searchParams: values, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

onMounted(() => {
  if (props.deviceInfo)
    searchForm.setFieldValue('deviceCode', props.deviceInfo.code)
  search()
})

watch(() => props.deviceInfo, () => {
  searchForm.setFieldValue('deviceCode', props.deviceInfo.code)
  search()
})

function openLog(id: string) {
  router.push(`/productionLog/${props.deviceInfo.type}/${props.deviceInfo.code}/${id}`)
}
</script>

<template>
  <PageContainer>
    <SearchBox :loading="loading" @submit="search">
      <FDatePicker name="startTime" label="开始时间" date-props.format="yy-MM-dd" />
      <FDatePicker name="endTime" label="结束时间" date-props.format="yy-MM-dd" />
    </SearchBox>

    <DataTable class="mt-4 w-full p-4" :value="data" lazy paginator data-key="id" :rows="pageData.pageSize" :total-records="total" @page="page">
      <Column field="filename" header="名称">
        <template #body="slotProps">
          <a class="cursor-pointer text-primary" @click="openLog(slotProps.data.id)">{{ slotProps.data.filename }}</a>
        </template>
      </Column>

      <Column header="时间">
        <template #body="slotProps">
          {{ formatDate(slotProps.data.logtime, 'YYYY-MM-DD HH:mm:ss') }}
        </template>
      </Column>
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
  </PageContainer>
</template>
