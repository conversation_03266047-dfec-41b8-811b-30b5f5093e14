<script setup lang="ts">
import { useFeedBackConfigUpdateForm } from './schema'
import UserSelectDialog from './UserSelectDialog.vue'
import { feedbackConfigApi } from '~/api/feedback/config'
import type { Reporter, Responder } from '~/api/feedback/config/type'
import type { User } from '~/api/wx-server/types'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{ save: [] }>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const userSelectOpen = ref(false)
const currentRespondentIndex = ref(-1)
const currentFieldType = ref<'respondent' | 'reporter'>('respondent')
const currentSelectedUsers = ref<User[]>([]) // 当前选中的用户

const { resetForm, handleSubmit, setValues, setFieldValue, values } = useFeedBackConfigUpdateForm()
const { push, remove, fields } = useFieldArray('responseConfig')
const { errorMessage } = useField('responseConfig')

function openUserSelect(index: number, fieldType: 'respondent' | 'reporter') {
  currentRespondentIndex.value = index
  currentFieldType.value = fieldType

  const responseConfig = [...(values.responseConfig || [])]

  // 根据字段类型获取当前已选用户
  if (responseConfig[index]) {
    if (fieldType === 'respondent' && responseConfig[index].responders) {
      // 将响应人转换为User对象格式
      currentSelectedUsers.value = responseConfig[index].responders.map((responder: Responder) => ({
        userid: responder.respondentId,
        name: responder.respondentName,
      })) as User[]
    }
    else if (fieldType === 'reporter' && responseConfig[index].reporters) {
      // 将告知人转换为User对象格式
      currentSelectedUsers.value = responseConfig[index].reporters.map((reporter: Reporter) => ({
        userid: reporter.reporterId,
        name: reporter.reportName,
      })) as User[]
    }
    else {
      currentSelectedUsers.value = []
    }
  }
  else {
    currentSelectedUsers.value = []
  }

  userSelectOpen.value = true
}

function handleUserSelected(users: User[]) {
  if (currentRespondentIndex.value >= 0) {
    const index = currentRespondentIndex.value
    const fieldType = currentFieldType.value

    const responseConfig = [...(values.responseConfig || [])]

    if (!responseConfig[index]) {
      responseConfig[index] = {
        pointTime: 0,
        responders: [],
        reporters: [],
      }
    }

    if (fieldType === 'respondent') {
      // 更新响应人数组
      responseConfig[index].responders = users.map(user => ({
        respondentId: user.userid,
        respondentName: user.name,
      }))
    }
    else if (fieldType === 'reporter') {
      // 更新告知人数组
      responseConfig[index].reporters = users.map(user => ({
        reporterId: user.userid,
        reportName: user.name,
      }))
    }

    // 设置响应配置
    setFieldValue('responseConfig', responseConfig)
  }
}

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await feedbackConfigApi.update(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function initializeForm() {
  if (props.id) {
    loading.value = true
    const res = await feedbackConfigApi.get(props.id)
    setValues(res)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="编辑快返配置" @show="initializeForm">
    <form @submit.prevent="save">
      <div class="space-y-6">
        <!-- 异常配置区块 -->
        <Fieldset legend="异常配置">
          <FormLayout>
            <LDictSelect name="lineCode" label="所属线体" code="LINE_CODE" />
            <LDictSelect name="anomaliesCode" label="异常大类" code="ABNORMAL_CATEGORY" class="w-full" />
          </FormLayout>
        </Fieldset>

        <!-- 响应配置区块 -->
        <Fieldset legend="响应配置">
          <DataTable :value="fields" data-key="key" class="w-full" responsive-layout="scroll">
            <Column header="响应时长">
              <template #body="{ index }">
                <PointTimeNumber :name="`responseConfig[${index}].pointTime`" />
              </template>
            </Column>

            <Column header="处理人">
              <template #body="{ index }">
                <div class="flex items-center gap-2">
                  <div class="h-[80px] flex-1 border-1 rounded-md p-1 border-surface md:w-56">
                    <RespondersList
                      :name="`responseConfig[${index}].responders`"
                      :removable="true"
                    />
                  </div>
                  <Button
                    icon="pi pi-user"
                    severity="secondary"
                    @click="openUserSelect(index, 'respondent')"
                  />
                </div>
              </template>
            </Column>

            <Column header="抄送人">
              <template #body="{ index }">
                <div class="flex items-center gap-2">
                  <div class="h-[80px] flex-1 border-1 rounded-md p-1 border-surface md:w-56">
                    <ReporterList
                      :name="`responseConfig[${index}].reporters`"
                      :removable="true"
                    />
                  </div>
                  <Button
                    icon="pi pi-user"
                    severity="secondary"
                    @click="openUserSelect(index, 'reporter')"
                  />
                </div>
              </template>
            </Column>

            <Column header="操作" width="100">
              <template #body="{ index }">
                <Button severity="danger" icon="pi pi-trash" outlined @click="remove(index)" />
              </template>
            </Column>
          </DataTable>

          <ErrorMsg :error-message="errorMessage" />
          <Button outlined class="mt-4 w-full" icon="pi pi-plus" label="添加配置项" @click="push({})" />
        </Fieldset>

        <!-- 操作按钮 -->
        <div class="mt-8 flex justify-end gap-4">
          <Button type="button" label="取消" severity="secondary" @click="open = false" />
          <Button type="submit" label="保存" :loading="loading" />
        </div>
      </div>
    </form>
  </Dialog>

  <UserSelectDialog
    v-model:open="userSelectOpen"
    :initial-selected-users="currentSelectedUsers"
    @select="handleUserSelected"
  />
</template>
