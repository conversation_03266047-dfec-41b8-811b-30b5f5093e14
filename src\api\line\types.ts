export interface ProductLine {
  name: string
  code: string
  type: LineType
}

export enum LineType {
  singleTrack,
  dualTrack,
}

export interface DeviceSearchParam {
  code: string
  startTime?: Date
  endTime?: Date
}

export enum DeviceType {
  PRINT_GKG = 'print_gkg',
  SMT_SAMSUNG = 'smt_samsung',
  SMT_NPM = 'smt_npm',
  SMT_NPM2 = 'smt_npm2',
  SMT_YAMAHA = 'smt_yamaha',
  AOI_JUZTE = 'aoi_juzte',
  AOI_YAMAHA = 'aoi_yamaha',
}

export interface DeviceInfo {
  name: string
  code: string
  ct: number
  type: DeviceType
}
