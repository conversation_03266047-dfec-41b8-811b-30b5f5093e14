export interface GkgPrintLog {
  id: string
  index: GkgPrintLogIndex
  filename: string
  logtime: Date
  record: GkgPrintLogRecord[]
  status: GkgPrintLogStatus[]

}

interface GkgPrintLogIndex {
  floor: string
  line: string
  machinetype: string
  vendor: string
  machineid: string
  machinesn: string
}

interface GkgPrintLogRecord {
  tag: string
  timestamp: string
  machinename: string
  barcode: string
  filename: string
  programrunning: string
  pcbnumber: string
  prodname: string
  prodtype: string
  cycletime: number
  spiresult2d: string
  printspeed: number
  frontsqgpress: number
  rearsqgpress: number
  printmode: string
  printgap: number
  snapoffdistance: number
  snapoffspeed: number
  snapoffdelay: number
  sqgupspeed: number
  sqgdownspeed: number
  sqgupfirst: string
  sqgheightatsnapoff: number
  cleaningafterlastboard: boolean
  cleaningfrequency: number
  cleaningspeed: number
  cleaningmode: string
  cleaningtype: string
  addspmode: string
  addspafterlastboard: number
  spiresult3d: string
  printdirection: string
  pcbsize: string
  tableupx: number
  tableupy1: number
  tableupy2: number
  xforward: number
  y1forward: number
  y2forward: number
  xbackward: number
  y1backward: number
  y2backward: number
  temperature: number
  humidity: number
  markdeviation: number
  tabletoprintingpos: number
  tabletosnapoffpos: number
}

interface GkgPrintLogStatus {
  barcode: string
  machinename: string
  status: string
  starttime: string
  endtime: string
  value: string
  value1: string
  value2: string
  value3: string
  value4: string
  value5: string
}
