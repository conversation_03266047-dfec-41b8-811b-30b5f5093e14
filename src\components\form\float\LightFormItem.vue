<script setup lang="ts">
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
}>()
const { errorMessage } = useField(props.name)
</script>

<template>
  <div class="flex flex-col justify-evenly px-2">
    <label for="name" class="flex items-center text-sm text-muted">{{ label }}</label>
    <div class="flex flex-col gap-1">
      <slot />
      <ErrorMsg :error-message="errorMessage" />
    </div>
  </div>
</template>
