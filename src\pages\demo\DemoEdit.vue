<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { demoOptions, useDemoData, useDemoForm } from './schema'

const props = defineProps<{
  id?: string
}>()

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, setValues, handleSubmit } = useDemoForm()
const { push, remove, fields } = useFieldArray('items')

const api = await useDemoData()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await api.update(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function onShow() {
  resetForm()
  if (props.id) {
    try {
      loading.value = true
      const data = await api.get(props.id)
      setValues(data)
    }
    finally {
      loading.value = false
    }
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="更新demo" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <FInput name="name" label="名称" />
        <FInput name="description" label="描述" />
        <FDatePicker name="date1" label="日期1" />
        <FDatePicker name="date2" label="日期2" />
        <FSelect name="option1" label="选项1" :options="demoOptions" />
        <FSelect name="option2" label="选项2" :options="demoOptions" />
        <LRadioGroup name="radio" label="选项3" :options="demoOptions" />
        <LBoolRadioGroup name="yesno" label="是否" />
        <FDatePicker :date-props="{ timeOnly: true }" name="time" label="时间" />
      </FormLayout>
      <Button @click="push({})">
        新增
      </Button>
      <DataTable :value="fields">
        <Column header="名称">
          <template #body="slotProps">
            <FInput :name="`items[${slotProps.index}].label`" label="名称" />
          </template>
        </Column>
        <Column header="选项1">
          <template #body="slotProps">
            <FSelect :name="`items[${slotProps.index}].option`" label="选项1" :options="demoOptions" />
          </template>
        </Column>
        <Column header="日期">
          <template #body="slotProps">
            <FDatePicker :name="`items[${slotProps.index}].date`" label="日期" />
          </template>
        </Column>
        <Column header="操作">
          <template #body="slotProps">
            <Button @click="remove(slotProps.index)">
              删除
            </Button>
          </template>
        </Column>
      </DataTable>

      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
