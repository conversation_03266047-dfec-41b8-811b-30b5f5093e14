import { HTTPError } from 'ky'
import { workshopApi } from '~/api/workshop/index'
import type { Workshop } from '~/layout/index/types'
import { useLayoutStore } from '~/stores/layout'

interface WorkShopState {
  staticWorkShopDesktopInactive: boolean
  overlayWorkShopActive: boolean
  profileSidebarVisible: boolean
  configSidebarVisible: boolean
  staticWorkShopMobileActive: boolean
  workShopHoverActive: boolean
  activeWorkShopItemKey: null | string
}

const workshopValue = ref<Workshop[]>([])

const workshopState = reactive<WorkShopState>({
  staticWorkShopDesktopInactive: false,
  overlayWorkShopActive: false,
  profileSidebarVisible: false,
  configSidebarVisible: false,
  staticWorkShopMobileActive: false,
  workShopHoverActive: false,
  activeWorkShopItemKey: null,
})

const activeWorkShopItem = computed(() => {
  return workshopValue.value.find(o => o.code === unref(workshopState.activeWorkShopItemKey)) || null
})

// 获取全部车间
async function init() {
  try {
    const res = await workshopApi.list()
    workshopValue.value = res
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(ex.message)
    }
  }
}

init()

export function useWorkshop() {
  // 切换产品线
  const onWorkshopToggle = () => {
    const { layoutConfig } = useLayoutStore()
    if (layoutConfig.menuMode === 'overlay') {
      workshopState.overlayWorkShopActive = !workshopState.overlayWorkShopActive
    }

    if (window.innerWidth > 991) {
      workshopState.staticWorkShopDesktopInactive = !workshopState.staticWorkShopDesktopInactive
    }
    else {
      workshopState.staticWorkShopMobileActive = !workshopState.staticWorkShopMobileActive
    }
  }

  // 设置产品线
  const setActiveWorkShopItem = async (itemKey: MaybeRef<string | null>) => {
    workshopState.activeWorkShopItemKey = unref(itemKey)
  }

  // 重置产品线
  const resetWorkShop = () => {
    workshopState.overlayWorkShopActive = false
    workshopState.staticWorkShopMobileActive = false
    workshopState.workShopHoverActive = false
  }

  const isSidebarActive = computed(() => workshopState.overlayWorkShopActive || workshopState.staticWorkShopMobileActive)
  const workshop = computed(() => workshopValue.value)

  return { setActiveWorkShopItem, resetWorkShop, isSidebarActive, workshop, onWorkshopToggle, workshopState, activeWorkShopItem, init }
}
