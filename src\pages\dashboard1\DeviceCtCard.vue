<script setup lang="ts">
import { onMounted, ref } from 'vue'
import DeviceCtGauge from '~/components/charts/DeviceCtGauge.vue'
import { deviceApi } from '~/api/device'

interface Props {
  device: {
    code: string
    type: string
    actualCt: number
    theoreticalCt: number
    trackName: string
    bottleneck: boolean
  }
}

const props = defineProps<Props>()
const deviceStatus = ref<number>(0)

async function fetchDeviceStatus() {
  try {
    const { status } = await deviceApi.getClientStatus(props.device.code)
    deviceStatus.value = status
  }
  catch (error) {
    console.error('Failed to fetch device status:', error)
  }
}

onMounted(() => {
  fetchDeviceStatus()
})
</script>

<template>
  <div class="relative flex flex-col items-center justify-center">
    <div class="absolute right-2 top-2 flex items-center gap-2">
      <div
        class="h-2 w-2 rounded-full"
        :class="deviceStatus === 1 ? 'bg-green-500' : 'bg-red-500'"
      />
      <!-- <span class="text-sm text-gray-500">
        {{ deviceStatus === 1 ? '在线' : '离线' }}
      </span> -->
    </div>
    <DeviceCtGauge
      :actual="device.actualCt"
      :theoretical="device.theoreticalCt"
      :device-info="{ type: device.type, code: device.code, trackName: device.trackName }"
      :bottleneck="device.bottleneck"
    />
    <span class="font-semibold">
      理论CT: {{ device.theoreticalCt }}s
    </span>
  </div>
</template>
