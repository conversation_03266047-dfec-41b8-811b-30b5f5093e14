<script setup>
import IndexMenuItem from './IndexMenuItem.vue'

const { workshop } = useWorkshop()
</script>

<template>
  <div class="layout-menu">
    <ul>
      <li class="layout-root-menuitem">
        <div class="layout-menuitem-root-text">
          车间选择
        </div>
      </li>
      <template v-for="(item, i) in workshop" :key="item">
        <IndexMenuItem :item="item" :index="i" />
      </template>
    </ul>
  </div>
</template>
