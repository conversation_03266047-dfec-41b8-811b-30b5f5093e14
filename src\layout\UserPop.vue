<script setup lang="ts">
import { storeToRefs } from 'pinia'

import Popover from 'primevue/popover'
import UserPanel from './UserPanel.vue'
import { useUserStore } from '~/stores/user'

const { hasLogin } = storeToRefs(useUserStore())
const userPanelRef = ref()
function toggleUserPanel(event: any) {
  userPanelRef.value?.toggle(event)
}
</script>

<template>
  <button v-show="hasLogin" type="button" class="layout-topbar-action" @click="toggleUserPanel">
    <i class="pi pi-user" />
    <span>Profile</span>
  </button>
  <!-- <Avatar v-show="hasLogin" shape="circle" class="cursor-pointer" :image="Logo" /> -->
  <Popover ref="userPanelRef">
    <UserPanel @click="toggleUserPanel" />
  </Popover>
</template>
