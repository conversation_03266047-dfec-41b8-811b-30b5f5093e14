<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'

defineProps({
  /**
   * The minimum height for the placeholder element.
   * This is important to prevent layout shifts as content loads.
   * It should be a reasonable estimate of the final content height.
   */
  minHeight: {
    type: String,
    default: '300px',
  },
})

const container = ref<HTMLElement | null>(null)
const isVisible = ref(false)

let observer: IntersectionObserver | null = null

onMounted(() => {
  if (!container.value)
    return

  // Create an observer to detect when the component enters the viewport
  observer = new IntersectionObserver(
    ([entry]) => {
      // If the component is intersecting the viewport, mark it as visible
      if (entry.isIntersecting) {
        isVisible.value = true
        // Once visible, we no longer need to observe it, so disconnect
        if (container.value)
          observer?.unobserve(container.value)
      }
    },
    {
      // Start loading the component when it's 100px away from the viewport
      rootMargin: '100px',
      threshold: 0,
    },
  )

  observer.observe(container.value)
})

// Clean up the observer when the component is unmounted
onUnmounted(() => {
  if (observer)
    observer.disconnect()
})
</script>

<template>
  <div ref="container" :style="{ minHeight: !isVisible ? minHeight : 'auto' }">
    <!-- The content is only rendered when isVisible is true -->
    <slot v-if="isVisible" />
    <!-- Otherwise, a placeholder is shown -->
    <div v-else class="h-full flex items-center justify-center rounded-lg bg-surface-ground">
      <p class="text-muted-color">
        Loading...
      </p>
    </div>
  </div>
</template>
