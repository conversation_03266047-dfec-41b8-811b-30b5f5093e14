import { defineStore } from 'pinia'
import { useLayoutStore } from './layout'
import { menuApi } from '~/api/common/menu'
import type { MenuItemExt } from '~/layout/menu'

export const useMenuStore = defineStore('menu', () => {
  const menuValue = ref<MenuItemExt[]>([])

  interface MenuState {
    staticMenuDesktopInactive: boolean
    overlayMenuActive: boolean
    profileSidebarVisible: boolean
    configSidebarVisible: boolean
    staticMenuMobileActive: boolean
    menuHoverActive: boolean
    activeMenuItemKey: null | string
    activeMenuItem: MenuItemExt | null
  }
  const menuState = ref<MenuState>({
    staticMenuDesktopInactive: false,
    overlayMenuActive: false,
    profileSidebarVisible: false,
    configSidebarVisible: false,
    staticMenuMobileActive: false,
    menuHoverActive: false,
    activeMenuItem: null,
    activeMenuItemKey: null,
  })

  async function fetchMenu() {
    const menuData = await menuApi.getUserMenu()
    // prime vue menu data
    const primeMenuData = menuData.map((o) => {
      return {
        ...o,
        label: o.title,
        path: o.path,
        items: [],
        root: false,
      } as MenuItemExt
    })
    // all menu map  id -> menuData
    const menuMap = new Map<string, MenuItemExt>()
    // id -> parentMenuData
    const parentMenuMap = new Map<string, MenuItemExt>()

    primeMenuData.forEach((o) => {
      menuMap.set(o.id, o)
      if (o.parentId) {
        parentMenuMap.set(o.parentId, o)
      }
    })
    primeMenuData.forEach((o) => {
      if (o.parentId) {
        o.menuPath = `${menuMap.get(o.parentId)?.label}/${o.label}`
        menuMap.get(o.parentId)?.items?.push(o)
      }
      else {
        o.root = true
      }
    })
    primeMenuData.forEach((o) => {
      if (o.items?.length === 0) {
        o.items = undefined
      }
    })
    const result = primeMenuData.filter(o => !o.parentId)
    menuValue.value = result
  }

  const onMenuToggle = () => {
    const { layoutConfig } = useLayoutStore()
    if (layoutConfig.menuMode === 'overlay') {
      menuState.value.overlayMenuActive = !menuState.value.overlayMenuActive
    }

    if (window.innerWidth > 991) {
      menuState.value.staticMenuDesktopInactive = !menuState.value.staticMenuDesktopInactive
    }
    else {
      menuState.value.staticMenuMobileActive = !menuState.value.staticMenuMobileActive
    }
  }

  const setActiveMenuItem = (itemKey: MaybeRef<string | null>, menuItem: MenuItemExt) => {
    menuState.value.activeMenuItemKey = unref(itemKey)
    menuState.value.activeMenuItem = menuItem
  }

  // 根据路径查找菜单项
  const findMenuItemByPath = (path: string): MenuItemExt | null => {
    const findInMenu = (items: MenuItemExt[]): MenuItemExt | null => {
      for (const item of items) {
        if (item.path === path) {
          return item
        }
        if (item.items && item.items.length > 0) {
          const found = findInMenu(item.items)
          if (found)
            return found
        }
      }
      return null
    }
    return findInMenu(menuValue.value)
  }

  // 根据路径设置激活菜单项
  const setActiveMenuItemByPath = (path: string) => {
    const menuItem = findMenuItemByPath(path)
    if (menuItem) {
      // 需要计算正确的 itemKey
      const calculateItemKey = (targetItem: MenuItemExt, items: MenuItemExt[], parentKey?: string): string | null => {
        for (let i = 0; i < items.length; i++) {
          const item = items[i]
          const currentKey = parentKey ? `${parentKey}-${i}` : String(i)

          if (item === targetItem) {
            return currentKey
          }

          if (item.items && item.items.length > 0) {
            const found = calculateItemKey(targetItem, item.items, currentKey)
            if (found)
              return found
          }
        }
        return null
      }

      const itemKey = calculateItemKey(menuItem, menuValue.value)
      if (itemKey) {
        setActiveMenuItem(itemKey, menuItem)
      }
    }
  }

  const resetMenu = () => {
    menuState.value.overlayMenuActive = false
    menuState.value.staticMenuMobileActive = false
    menuState.value.menuHoverActive = false
  }

  const isSidebarActive = computed(() => menuState.value.overlayMenuActive || menuState.value.staticMenuMobileActive)

  const menu = computed(() => menuValue.value)
  return { menu, menuState, fetchMenu, resetMenu, isSidebarActive, setActiveMenuItem, setActiveMenuItemByPath, findMenuItemByPath, onMenuToggle }
}, {
  persistedState: {
    persist: false,
  },
})
