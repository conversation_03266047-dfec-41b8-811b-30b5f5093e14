<script setup lang="ts">
import { useAchievementRateCreateForm } from './schema'
import { achievementRateApi } from '~/api/achievement-rate'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit } = useAchievementRateCreateForm()
const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await achievementRateApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})
function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建目标达成率" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LDictSelect name="lineCode" label="线体编码" code="LINE_CODE" />
        <LInputNumber name="rate" label="达成率" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
