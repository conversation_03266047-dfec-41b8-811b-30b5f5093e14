<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { useUserCreateForm } from './schema'
import { userApi } from '~/api/common/user'
import LInput from '~/components/form/label/LInput.vue'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit } = useUserCreateForm()

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await userApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="添加用户" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="用户名" />
        <LInput name="displayName" label="姓名" />
        <LInput name="password" label="密码" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
