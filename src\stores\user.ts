import { defineStore } from 'pinia'
import { useMenuStore } from './menu'
import { authApi } from '~/api/common/auth'

export interface User {
  id: string
  name: string
  displayName: string
  deptId: string
  deptName: string
  isAdmin: boolean
  avatar: string
  perms: string[]
  properties?: Record<string, string>
}

export const useUserStore = defineStore('user', () => {
  const userValue = ref<User>()
  const perms = computed(() => userValue.value?.perms)
  const isAdmin = computed(() => userValue.value?.isAdmin)
  const user = computed(() => userValue.value)
  const hasLogin = computed(() => userValue.value !== undefined && userValue.value !== null)

  const { fetchMenu } = useMenuStore()
  async function getUser() {
    if (user.value) {
      return user
    }
    else {
      await fetchUser()
      return user
    }
  }

  async function fetchUser() {
    userValue.value = await authApi.getUserInfo()
  }

  function login(accessToken: string, refreshToken: string, user: User) {
    localStorage.setItem(import.meta.env.VITE_APP_REFRESH_STORE, refreshToken)
    localStorage.setItem(import.meta.env.VITE_APP_ACCESS_STORE, accessToken)
    userValue.value = user
    fetchMenu()
  }

  async function logout() {
    try {
      // todo request logout api
    }
    finally {
      clear()
    }
  }

  function clear() {
    userValue.value = undefined
    localStorage.removeItem(import.meta.env.VITE_APP_ACCESS_STORE)
    localStorage.removeItem(import.meta.env.VITE_APP_REFRESH_STORE)
  }

  return {
    login,
    logout,
    clear,
    getUser,
    fetchUser,
    user,
    perms,
    isAdmin,
    hasLogin,
  }
})
