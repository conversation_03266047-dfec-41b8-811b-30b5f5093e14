import { toTypedSchema } from '@vee-validate/zod'

// 查询达成率
const AchievementRateSearchSchema = toTypedSchema(z.object({
  lineCode: z.string().optional(),
}))

// 创建达成率
const AchievementRateCreateSchema = toTypedSchema(z.object({
  lineCode: z.string({
    required_error: '请输入线体编码',
  }),
  rate: z.number({
    required_error: '请输入计划达成率',
  }),
}))

const AchievementRateEditSchema = toTypedSchema(z.object({
  lineCode: z.string({
    required_error: '请输入线体编码',
  }),
  rate: z.number({
    required_error: '请输入计划达成率',
  }),
}))

export function useAchievementRateSearchForm() {
  const form = useForm({
    validationSchema: AchievementRateSearchSchema,
  })
  return form
}

export function useAchievementRateCreateForm() {
  const form = useForm({
    validationSchema: AchievementRateCreateSchema,
  })
  return form
}

export function useAchievementRateEditForm() {
  const form = useForm({
    validationSchema: AchievementRateEditSchema,
  })
  return form
}
