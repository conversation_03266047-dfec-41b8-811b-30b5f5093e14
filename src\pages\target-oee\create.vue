<script setup lang="ts">
import { useTargetOeeCreateForm } from './schema'
import { targetOeeApi } from '~/api/target-oee'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit } = useTargetOeeCreateForm()

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await targetOeeApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建OEE值" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInputNumber name="availability" label="稼动率" />
        <LInputNumber name="performance" label="运行效率" />
        <LInputNumber name="quality" label="良品率" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
