<script setup lang="ts">
import { formatDate } from '@vueuse/core'
import { HTTPError } from 'ky'
import { logApi } from '~/api/log'

import type { YamahaSmtLog } from '~/api/log/device/yamahaSmt'

const props = defineProps<{
  deviceCode: string
  id: string
}>()

const data = ref<YamahaSmtLog>()

watch(() => props, async () => {
  search()
})

async function search() {
  try {
    const res = await logApi.getProductionLog({
      deviceCode: props.deviceCode,
      id: props.id,
    })
    data.value = res
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(ex.message)
    }
  }
}

onMounted(() => {
  search()
})
</script>

<template>
  <div class="w-full p-4">
    <div class="mb-4 w-full flex justify-center text-2xl font-bold">
      YAMAHA SMT LOG
    </div>
    <Panel class="mb-4 w-full">
      <div class="grid grid-cols-8 gap-4 p-4">
        <div>基板名 </div><div>{{ data?.substratename }}</div>
        <div>基板备注 </div><div>{{ data?.substrateremark }}</div>
        <div>生产开始时间 </div><div>{{ data ? formatDate(data.productionstarttime, 'YYYY-MM-DD HH:mm:ss') : '' }}</div>
        <div>生产结束时间 </div><div>{{ data ? formatDate(data.productionendtime, 'YYYY-MM-DD HH:mm:ss') : '' }}</div>
        <div>预定张数 </div><div>{{ data?.plannedsheets }}</div>
        <div>批内序号 </div><div>{{ data?.batchsequencenumber }}</div>
        <div>结束标志 </div><div>{{ data?.endflag }}</div>
        <div>贴装ct(秒) a </div><div>{{ data?.mountingcta }}</div>
        <div>贴装ct(秒) b </div><div>{{ data?.mountingctb }}</div>
        <div>贴装ct(秒) c </div><div>{{ data?.mountingctc }}</div>
        <div>贴装ct(秒) d </div><div>{{ data?.mountingctd }}</div>
        <div>传送ct(秒) </div><div>{{ data?.transferct }}</div>
        <div>待机ct(秒) </div><div>{{ data?.standbyct }}</div>
        <div>标记识别ct(秒) a </div><div>{{ data?.markrecognitioncta }}</div>
        <div>标记识别ct(秒) b </div><div>{{ data?.markrecognitionctb }}</div>
        <div>标记识别ct(秒) c </div><div>{{ data?.markrecognitionctc }}</div>
        <div>标记识别ct(秒) d </div><div>{{ data?.markrecognitionctd }}</div>
        <div>吸料出错次数 </div><div>{{ data?.suctionerrorcount }}</div>
        <div>元件识别出错次数 </div><div>{{ data?.componentrecognitionerror }}</div>
        <div>翘脚错误次数 </div><div>{{ data?.liftingfooterrorcount }}</div>
        <div>标记识别出错次数 </div><div>{{ data?.markrecognitionerrorcount }}</div>
        <div>传送出错次数 </div><div>{{ data?.transfererrorcount }}</div>
        <div>其它出错次数 </div><div>{{ data?.othererrorcount }}</div>
        <div>出错停机次数 </div><div>{{ data?.errorshutdowncount }}</div>
        <div>出错停机时间(秒) </div><div>{{ data?.errorshutdowntime }}</div>
        <div>出错修复时间(秒) </div><div>{{ data?.errorrecoverytime }}</div>
        <div>坏板标记ng数 </div><div>{{ data?.badboardngcount }}</div>
        <div>完成贴装的拼板数 </div><div>{{ data?.completedpanelcount }}</div>
        <div>输送台 </div><div>{{ data?.conveyingtable }}</div>
        <div>因上游待机的时间(秒) </div><div>{{ data?.upstreamstandbytime }}</div>
        <div>因下游待机的时间(秒) </div><div>{{ data?.downstreamstandbytime }}</div>
        <div>因操作员停机的时间(秒) </div><div>{{ data?.operatorshutdowntime }}</div>
        <div>轨道 </div><div>{{ data?.track }}</div>
        <div>等待其它轨道的时间(秒) </div><div>{{ data?.awaitingothertracktime }}</div>
        <div>基板id </div><div>{{ data?.substrateid }}</div>
        <div>--- </div><div>{{ data?.placeholder1 }}</div>
        <div>传送停止偏移量 </div><div>{{ data?.transferstopoffset }}</div>
        <div>传送示教执行标志 </div><div>{{ data?.transferteachingflag }}</div>
        <div>生产品种 </div><div>{{ data?.productionvariety }}</div>
        <div>基板面信息 </div><div>{{ data?.substratesurfaceinfo }}</div>
        <div>生产批次 </div><div>{{ data?.productionbatch }}</div>
        <div>--- </div><div>{{ data?.placeholder2 }}</div>
        <div>因其它输送台待机的时间(秒) </div><div>{{ data?.otherconveyingtabletime }}</div>
        <div>--- </div><div>{{ data?.placeholder3 }}</div>
        <div>--- </div><div>{{ data?.placeholder4 }}</div>
        <div>--- </div><div>{{ data?.placeholder5 }}</div>
        <div>--- </div><div>{{ data?.placeholder6 }}</div>
        <div>--- </div><div>{{ data?.placeholder7 }}</div>
        <div>--- </div><div>{{ data?.placeholder8 }}</div>
      </div>
    </Panel>
  </div>
</template>
