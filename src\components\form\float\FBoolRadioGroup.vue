<script setup lang="ts">
const props = defineProps<{
  name: string
  label: string
}>()

const { value } = useField<boolean | undefined>(() => props.name)
</script>

<template>
  <LightFormItem :name="name" :label="label">
    <div class="flex flex-wrap gap-4">
      <div class="flex gap-2">
        <RadioButton v-model="value" input-id="yes" :name="name" :value="true" />
        <label for="yes">是</label>
      </div>
      <div class="flex gap-2">
        <RadioButton v-model="value" input-id="no" :name="name" :value="false" />
        <label for="no">否</label>
      </div>
    </div>
  </LightFormItem>
</template>
