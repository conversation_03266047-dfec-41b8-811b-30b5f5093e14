<script setup lang="ts">
import { useAbnormalSearchForm } from './schema'
import { abnormalApi } from '~/api/abnormal'
import type { Abnormal } from '~/api/abnormal/types'
import type { PageData } from '~/api/common/type'

const loading = ref<boolean>(false)
const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})
const data = ref<Abnormal[]>([])
const total = ref(0)

const searchForm = useAbnormalSearchForm()
const search = searchForm.handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await abnormalApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

onMounted(() => {
  search()
})
</script>

<template>
  <div class="mt-2 h-full text-sm card !pb-0">
    <div class="mb-4 text-lg font-semibold">
      系统管理/换线时间
    </div>
  </div>
  <PageContainer class="mt-2 text-sm">
    <SearchBox :loading="loading" @submit="search" @search="search">
      <FDictSelect name="workshopCode" label="车间编码" code="WorkShop_Code" />
      <FDatePicker name="writeStartTime" label="填写开始时间" :date-props="{ showTime: true, showSeconds: true }" />
      <FDatePicker name="writeEndTime" label="填写结束时间" :date-props="{ showTime: true, showSeconds: true }" />
    </SearchBox>
  </PageContainer>
</template>
