<script setup lang="ts">
import { formatDate } from '@vueuse/core'
import { HTTPError } from 'ky'
import { logApi } from '~/api/log'
import type { JutzeAoiLog } from '~/api/log/device/jutzeAoi'

const props = defineProps<{
  deviceCode: string
  id: string
}>()

const data = ref<JutzeAoiLog>()

watch(() => props, async () => {
  search()
})

async function search() {
  try {
    const res = await logApi.getProductionLog({
      deviceCode: props.deviceCode,
      id: props.id,
    })
    data.value = res
  }
  catch (ex) {
    if (ex instanceof HTTPError) {
      error(ex.message)
    }
  }
}

onMounted(() => {
  search()
})
</script>

<template>
  <div class="w-full p-4">
    <div class="mb-4 w-full flex justify-center text-2xl font-bold">
      JUTZE AOI LOG
    </div>
    <Panel header="基本信息" class="mb-4 w-full">
      <div class="grid grid-cols-8 gap-4 p-4">
        <div> 日志名称 </div><div>{{ data?.filename }}</div>
        <div>日志时间</div><div>{{ data?.logtime ? formatDate(data?.logtime, 'YYYY-MM-DD HH:mm:ss') : '' }}</div>
        <div>日志类型</div><div>{{ data?.type }}</div>
      </div>
    </Panel>
    <Panel header="检测记录" class="mb-4 w-full">
      <DataTable :value="data?.record" :rows="10" paginator scrollable scroll-height="800px">
        <Column field="serialnumber" header=" 序列号" />
        <Column field="datetime" header="时间">
          <template #body="slotProps">
            {{ slotProps.data.datetime ? formatDate(slotProps.data.datetime, 'YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </Column>
        <Column field="testresult" header="检测结果" />
        <Column field="errorcode" header=" 错误码" />
        <Column field="xcoordinate" header=" x坐标" />
        <Column field="ycoordinate" header=" y坐标" />
        <Column field="defecttype" header="缺陷类型" />
        <Column field="defectcode" header=" 缺陷编码" />
        <Column field="softwareversion" header=" 软件版本" />
      </DataTable>
    </Panel>
  </div>
</template>
