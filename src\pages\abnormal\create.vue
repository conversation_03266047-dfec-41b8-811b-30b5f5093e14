<script setup lang="ts">
import { useAbnormalCreateForm } from './schema'
import { abnormalApi } from '~/api/abnormal'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit } = useAbnormalCreateForm()

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await abnormalApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="异常创建" @show="onShow">
    <form @submit="save">
      <FormVertical>
        <LDictSelect name="lineCode" label="线体编码" code="LINE_CODE" />
        <LDictSelect name="classification" label="异常分类" code="ABNORMAL_CODE" />
        <LDatePicker name="startTime" label="开始时间" :date-props="{ showTime: true, showSeconds: true }" />
        <LDatePicker name="endTime" label="结束时间" :date-props="{ showTime: true, showSeconds: true }" />
        <LInput name="cause" label="异常原因" />
        <LInput name="responsible" label="责任人" />
        <LTextarea class="grid-col-span-2" name="solution" label="解决方案" />
        <LInput name="department" label="责任部门" />
        <LInput name="writer" label="填写人" />
      </FormVertical>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
