<script setup lang="ts">
import <PERSON>ton from 'primevue/button'
import Dialog from 'primevue/dialog'

import { useUserUpdateForm } from './schema'
import { userApi } from '~/api/common/user'

const props = defineProps<{
  id?: string
}>()

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, setValues, handleSubmit } = useUserUpdateForm()
const { push, remove, fields } = useFieldArray('perms')

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await userApi.update(props.id, values)
      success('编辑成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function onShow() {
  resetForm()
  if (props.id) {
    try {
      loading.value = true
      const data = await userApi.get(props.id)
      setValues(data)
    }
    finally {
      loading.value = false
    }
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="编辑用户" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <FInput name="displayName" label="姓名" />
      </FormLayout>

      <Fieldset legend="权限">
        <DataTable :value="fields">
          <Column header="名称">
            <template #body="slotProps">
              <SInput :name="`perms[${slotProps.index}]`" label="名称" />
            </template>
          </Column>

          <Column header="操作">
            <template #body="slotProps">
              <Button severity="danger" icon="pi pi-trash" outlined @click="remove(slotProps.index)" />
            </template>
          </Column>
        </DataTable>
        <Button size="large" outlined fluid class="pi pi-plus mt-4" @click="push('')" />
      </Fieldset>

      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
