export enum DeviceType {
  PRINT_GKG = 'print_gkg', // gkg打印机
  PRINT_DEK = 'print_dek', // dek打印机
  SMT_SAMSUNG = 'smt_samsung', // 三星贴片机
  SMT_NPM = 'smt_npm', // 松下贴片机
  SMT_NPM_REPORTER = 'smt_npm_reporter', // 松下贴片机报表
  SMT_NPM_REPORTER2 = 'smt_npm_reporter2', // 松下贴片机2
  SMT_YAMAHA = 'smt_yamaha', // 雅马哈贴片机
  AOI_JUTZE = 'aoi_jutze', // 矩子aoi
  AOI_YAMAHA = 'aoi_yamaha', // 雅马哈aoi
  AOI_VISCOM = 'aoi_viscom', // viscom_aoi
  AOI_DELU = 'aoi_delu', // delu_aoi
}

export enum DeviceCategory {
  AOI = 'AOI',
  SMT = 'SMT',
  PRINTER = 'PRINTER',
  SPI = 'SPI',
}

export enum DeviceTrack {
  TRACK1 = 'track1', // 轨道1
  TRACK2 = 'track2', // 轨道2
}

export interface DeviceQuery {
  /** 页码 */
  pageNum?: number
  /** 每页数量 */
  pageSize?: number
  /** 设备编码 */
  name?: string
  /** 设备类型 */
  code?: string
  /** 线体编码 */
  lineId?: string
}

export interface Device {
  id: string
  name: string
  code: string
  type: DeviceType
  category: DeviceCategory
  track: string
  lineId: string
  clientIp: string
  clientPort: string
  sort: number
  group: number
  enable: number
}

export interface DeviceCreateParam {
  name: string
  code: string
  type: DeviceType
  category: DeviceCategory
  track: string
  lineId: string
  clientIp: string
  clientPort: string
  sort: number
  group: number
  enable: number
}
