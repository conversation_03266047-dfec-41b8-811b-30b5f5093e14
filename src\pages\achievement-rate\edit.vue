<script setup lang="ts">
import { useAchievementRateEditForm } from './schema'
import { achievementRateApi } from '~/api/achievement-rate'
import type { AchievementRate } from '~/api/achievement-rate/type'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit, setValues } = useAchievementRateEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await achievementRateApi.update(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const achievementRate: AchievementRate = await achievementRateApi.get(props.id)
    setValues(achievementRate)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="编辑目标达成率" @show="loadData">
    <form @submit="save">
      <FormLayout>
        <LInput name="lineCode" label="线体编码" />
        <LInputNumber name="rate" label="达成率" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
