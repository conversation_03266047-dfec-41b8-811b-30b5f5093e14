import type { ResponseConfig } from '../config/type'
import type { Reporter, Responder } from '../types'

/** 发送状态枚举 */
export type SendStatus = 'UNSENT' | 'SENT' | 'STOPPED'

/** 发送状态选项 */
export const SEND_STATUS_OPTIONS = [
  { label: '未发送', value: 'UNSENT' as SendStatus },
  { label: '已发送', value: 'SENT' as SendStatus },
  { label: '停止发送', value: 'STOPPED' as SendStatus },
]

/** 快返触发发送表 */
export interface FeedbackTriggerSend {
  id: string // 主键ID
  expectedSendTime: Date // 预期发送时间
  sendTime?: Date // 发送时间
  responders: Responder[] // 响应人
  reporters: Reporter[] // 通知人
  responseConfig: ResponseConfig
  sendResult?: string // 发送结果
  triggerRecordId: string // 触发记录ID
  sendInfo?: string // 发送信息
  sendStatus: SendStatus // 发送状态
}

export interface FeedbackTriggerSendCreateParam {
  expectedSendTime: Date // 预期发送时间
  sendUserId: string[] // 响应人ID列表
  reportUserId: string[] // 告知人ID列表
  triggerRecordId: string // 触发记录ID
  sendInfo?: string // 发送信息
  sendStatus: SendStatus // 发送状态
}

export interface FeedbackTriggerSendUpdateParam extends FeedbackTriggerSendCreateParam {
  sendTime?: Date // 发送时间
  sendResult?: string // 发送结果
}

export interface FeedbackTriggerSendSearchParam {
  triggerRecordId?: string // 触发记录ID
  sendStatus?: SendStatus // 发送状态
  expectedSendStartTime?: Date // 预期发送开始时间
  expectedSendEndTime?: Date // 预期发送结束时间
  sendStartTime?: Date // 发送开始时间
  sendEndTime?: Date // 发送结束时间
}

export interface FeedbackTriggerSendWithId extends FeedbackTriggerSend {
  id: string // 主键ID
}
