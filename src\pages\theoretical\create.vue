<script setup lang="ts">
import { useTheoreticalCreateForm } from './schema'
import { theoreticalApi } from '~/api/theoretical'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit } = useTheoreticalCreateForm()
const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await theoreticalApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})
function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建理论值" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="lineCode" label="线体编码" />
        <LInput name="deviceCode" label="设备编码" />
        <LInput name="deviceType" label="设备类型" />
        <LInput name="productModel" label="产品型号" />
        <LInputNumber name="ct" label="理论CT" />
        <LInputNumber name="flatNumber" label="拼板数" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
