/** 产品查询参数 */
export interface ProductQuery {
  /** 页码 */
  pageNum?: number
  /** 每页数量 */
  pageSize?: number
  /** 产品型号 */
  productModel?: string
}

/** 产品信息 */
export interface Product {
  /** ID */
  id: string
  /** 线体编码 */
  lineCode: string
  /** 产品型号 */
  productModel: string
  /** 点数 */
  pointNum: number
}

export interface ProductCreateParam {
  /** 线体编码 */
  lineCode?: string
  /** 产品型号 */
  productModel: string
  /** 点数 */
  pointNum: number
}
