<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { useDictForm } from './schema'
import { dictApi } from '~/api/common/dict'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit, setFieldValue } = useDictForm()
const { push, remove, fields } = useFieldArray('itemList')

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await dictApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
  setFieldValue('itemList', [])
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建字典" @show="onShow">
    <form @submit="save">
      <Fieldset legend="基本信息">
        <FormLayout>
          <LInput name="code" label="编码" />
          <LInput name="name" label="名称" />
        </FormLayout>
      </Fieldset>
      <Fieldset legend="表单项">
        <DataTable class="w-full" :value="fields" data-key="key">
          <Column header="编码">
            <template #body="slotProps">
              <SInput :name="`itemList[${slotProps.index}].value`" />
            </template>
          </Column>
          <Column header="名称">
            <template #body="slotProps">
              <SInput :name="`itemList[${slotProps.index}].label`" />
            </template>
          </Column>
          <Column header="操作">
            <template #body="slotProps">
              <Button severity="danger" icon="pi pi-trash" outlined @click="remove(slotProps.index)" />
            </template>
          </Column>
        </DataTable>
        <Button size="large" outlined fluid class="pi pi-plus mt-4" @click="push({})" />
      </Fieldset>

      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
