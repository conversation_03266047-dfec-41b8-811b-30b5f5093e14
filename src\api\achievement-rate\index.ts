import type { PageList, Pageable } from '../common/type'
import type { AchievementRate, AchievementRateCreate, AchievementRateEdit, AchievementRateSearch } from './type'
import { kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const achievementRateApi = {
  create: (param: AchievementRateCreate) => kyPost('achievement-rate', param),
  page: (param: Pageable<Partial<AchievementRateSearch>>) => kyPost('achievement-rate/page', param).json<PageList<AchievementRate>>(),
  delete: (id: string) => kyDelete(`achievement-rate/${id}`),
  get: (id: string) => kyGet(`achievement-rate/${id}`).json<AchievementRate>(),
  update: (id: string, param: AchievementRateEdit) => kyPut(`achievement-rate/${id}`, param),
}
