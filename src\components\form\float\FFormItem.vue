<script setup lang="ts">
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
}>()
const { errorMessage } = useField(props.name)
</script>

<template>
  <div class="flex flex-col gap-1 pt-6">
    <FloatLabel>
      <slot />
      <label :for="props.name">{{ label }}</label>
    </FloatLabel>
    <ErrorMsg :error-message="errorMessage" />
  </div>
</template>
