/** NPM事件代码查询参数 */
export interface NpmEventCodeQuery {
  /** 页码 */
  pageNum?: number
  /** 每页数量 */
  pageSize?: number
  /** 主代码 */
  mainCode?: string
  /** 子代码 */
  subCode?: string
  /** 事件类型 */
  eventType?: string
}

/** NPM事件代码信息 */
export interface NpmEventCode {
  /** ID */
  id: string
  /** 主代码 */
  mainCode: string
  /** 子代码 */
  subCode: string
  /** 主题目 */
  mainSubject: string
  /** 子题目 */
  subSubject: string
  /** 事件类型 */
  eventType: string
}

/** NPM事件代码创建参数 */
export interface NpmEventCodeCreateParam {
  /** 主代码 */
  mainCode: string
  /** 子代码 */
  subCode: string
  /** 主题目 */
  mainSubject: string
  /** 子题目 */
  subSubject: string
  /** 事件类型 */
  eventType?: string
}
