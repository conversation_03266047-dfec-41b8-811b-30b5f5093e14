<script setup lang="ts">
import { useRoute } from 'vue-router'
import { productInfoApi } from '~/api/product/info'

const htmlContent = ref('')
const route = useRoute()
const id = Number(route.query.id)

productInfoApi.get(id).then((res) => {
  htmlContent.value = res
})
</script>

<template>
  <div>
    <!-- 使用 v-html 指令来显示 HTML 内容 -->
    <div v-html="htmlContent" />
  </div>
</template>

<style scoped>
::v-deep .left {
  border-top-width: thin;
  border-bottom-width: thin;
  border-left-width: thin;
  border-right-width: thin;
  border-top-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-right-style: solid;
  display: table;
  border-collapse: separate;
  box-sizing: border-box;
  text-indent: initial;
  unicode-bidi: isolate;
  border-spacing: 2px;
  border-color: gray;
}
</style>
