import { type RouteRecordRaw, createRouter, createWebHistory } from 'vue-router'

import adminRoute from './admin'
import aggRoute from './aggregation'
import commonRoute from './common'
import logRoute from './log'
import oeeRoute from './oee'
import feedbackRoute from './feedback'
import { globalGuard } from './router-guard'

function buildRoute(routes: RouteRecordRaw[][]) {
  const result = routes.flatMap(o => o)
  return result
}

const routes = buildRoute([commonRoute, adminRoute, logRoute, feedbackRoute])
const layoutRoutes = [
  {
    path: '/layout',
    name: 'layout',
    component: () => import('~/layout/AppLayout.vue'),
    meta: {

    },
    children: routes.filter(o => !o.meta?.noLayout),
  },
  {
    path: '/indexlayout',
    name: 'indexlayout',
    component: () => import('~/layout/index/IndexLayout.vue'),
    meta: {
      isPublic: true,
    },
    children: aggRoute,
  },
  {
    path: '/dashboardlayout',
    name: 'dashboardlayout',
    component: () => import('~/layout/dashboard/DashboardLayout.vue'),
    meta: {
      isPublic: true,
    },
    children: oeeRoute,
  },

  ...routes.filter(o => o.meta?.noLayout),
] satisfies RouteRecordRaw[]

const router = createRouter({
  routes: layoutRoutes,
  history: createWebHistory(import.meta.env.BASE_URL),
})

globalGuard(router)

// router.beforeEach((to, from, next) => {
//   if (to.path === '/') {
//     loginApi.login().then((res) => {
//       if (res.code === 200) {
//         next('/admin/product/manage')
//       }
//     })
//   }
//   else {
//     next()
//   }
// })

export { router }
