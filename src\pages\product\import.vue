<script setup lang="ts">
import Dialog from 'primevue/dialog'
import FileUpload from 'primevue/fileupload'
import { useToast } from 'primevue/usetoast'
import { productApi } from '~/api/product_num'

const emit = defineEmits(['refresh'])
const open = defineModel<boolean>('open')
const fileupload = ref()
const toast = useToast()

async function onUpload(event: any) {
  try {
    const formData = new FormData()
    formData.append('file', event.files[0])

    await productApi.import(formData)

    toast.add({ severity: 'info', summary: 'Success', detail: '文件上传成功', life: 3000 })
    open.value = false
    emit('refresh')
    fileupload.value.clear()
  }
  catch (error: any) {
    toast.add({
      severity: 'error',
      summary: '上传失败',
      detail: error.message || '文件上传失败',
      life: 5000,
    })
  }
}

function onError(event: any) {
  const error = event.xhr?.response
    ? JSON.parse(event.xhr.response)
    : { message: '文件上传失败' }

  toast.add({
    severity: 'error',
    summary: '上传失败',
    detail: error.message,
    life: 5000,
  })
}
</script>

<template>
  <Dialog
    v-model:visible="open"
    modal
    header="导入数据"
    :style="{ width: '40rem' }"
    class="import-dialog"
  >
    <FileUpload
      ref="fileupload"
      :custom-upload="true"
      name="file"
      accept=".xlsx,.xls"
      choose-label="选择文件"
      upload-label="上传"
      cancel-label="取消"
      :max-file-size="1000000"
      class="import-uploader"
      @uploader="onUpload"
      @error="onError($event)"
    >
      <template #empty>
        <div class="upload-placeholder">
          <i class="pi pi-file-excel mb-2 text-3xl text-primary" />
          <p>拖拽Excel文件到这里或点击选择文件</p>
          <p class="text-sm text-gray-600">
            支持的文件格式：.xlsx, .xls
          </p>
        </div>
      </template>
    </FileUpload>
  </Dialog>
</template>

<style scoped>
.import-dialog :deep(.p-fileupload) {
  width: 100%;
}

.import-dialog :deep(.p-fileupload-content) {
  padding: 2rem;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border: 2px dashed var(--primary-color);
  border-radius: 6px;
  background-color: var(--surface-ground);
  transition: all 0.3s;
}

.upload-placeholder:hover {
  background-color: var(--surface-hover);
  border-color: var(--primary-600);
}

.import-dialog :deep(.p-fileupload-row) {
  margin: 0.5rem 0;
}

.import-dialog :deep(.p-button) {
  margin-right: 0.5rem;
}
</style>
