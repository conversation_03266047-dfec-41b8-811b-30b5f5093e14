<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { samsungInfoApi } from '~/api/device/samsung/info'

const content = ref('')
const route = useRoute()
const id = String(route.query.id)

samsungInfoApi.get(id).then((res) => {
  // 使用 JSON.stringify 格式化 JSON 数据，使其更具可读性
  content.value = JSON.stringify(res, null, 2)
})
</script>

<template>
  <div>
    <!-- 使用 <pre> 标签来保持格式和缩进 -->
    <pre>{{ content }}</pre>
  </div>
</template>

<style scoped></style>
