<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { useConfirm } from 'primevue/useconfirm'
import Create from './create.vue'
import Edit from './edit.vue'
import { useAchievementRateSearchForm } from './schema'
import { achievementRateApi } from '~/api/achievement-rate'
import type { AchievementRate } from '~/api/achievement-rate/type'
import type { PageData } from '~/api/common/type'

const loading = ref<boolean>(false)
const editId = ref<string>()
const open = reactive({
  create: false,
  edit: false,
  import: false,
})
const data = ref<AchievementRate[]>([])
const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})
const total = ref(0)
const searchForm = useAchievementRateSearchForm()
const search = searchForm.handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await achievementRateApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

const confirm = useConfirm()
function confirmDel(id: string, event: any) {
  confirm.require({
    target: event.currentTarget,
    message: '确认删除？',
    group: 'delete',
    accept: async () => {
      await achievementRateApi.delete(id)
      success('删除成功')
      data.value = data.value.filter(o => o.id !== id)
    },
  })
}

const selectedItems = ref<AchievementRate[]>([])

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

// 打开创建页面
function openCreatePage() {
  open.create = true
}

// 打开编辑页面
function openEditPage(id: string) {
  open.edit = true
  editId.value = id
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer class="mt-4">
    <!-- 搜索表单 -->
    <SearchBox :loading="loading" @submit="search" @search="search">
      <FDictSelect name="lineId" label="线体编码" code="LINE_CODE" />
    </SearchBox>
    <ButtonGroup class="pl-8">
      <Button outlined icon="pi pi-plus" @click="openCreatePage()" />
    </ButtonGroup>
    <!-- 数据表格 -->
    <DataTable
      v-model:selection="selectedItems"
      class="p-4"
      :value="data"
      lazy
      paginator
      data-key="id"
      :rows="pageData.pageSize"
      :total-records="total"
      @page="page"
    >
      <Column field="lineCode" header="线体编码" />
      <Column field="rate" header="达成率" />
      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-2">
            <Button outlined icon="pi pi-pencil" @click="openEditPage(slotProps.data.id)" />
            <Button outlined severity="danger" icon="pi pi-trash" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column>
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
    <Create v-model:open="open.create" @save="search" />
    <Edit :id="editId" v-model:open="open.edit" @save="search" />
  </PageContainer>
</template>
