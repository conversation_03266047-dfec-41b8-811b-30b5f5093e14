<script setup>
import { useDark } from '@vueuse/core'
import screenfull from 'screenfull'
import { useRouter } from 'vue-router'
import AppConfigurator from '../AppConfigurator.vue'
import logoDark from '~/assets/logo_dark.png'
import logoLight from '~/assets/logo_light.png'
import { useAggregationStore } from '~/stores/aggregation'

const { onWorkshopToggle } = useWorkshop()
const dark = useDark()
const logo = computed(() => (dark.value ? logoLight : logoDark))
const isFullscreen = ref(false)
const aggregationStore = useAggregationStore()
const router = useRouter()

// 全屏
function toggleFullscreen() {
  if (screenfull.isEnabled) {
    screenfull.toggle()
  }
}

// 切换页面
function togglePage() {
  const currentRoute = router.currentRoute.value
  const currentCode = currentRoute.params.code
  if (currentRoute.path.includes('fake')) {
    router.push({ name: 'home', params: { code: currentCode } })
  }
  else {
    router.push({ name: 'home1', params: { code: currentCode } })
  }
}
</script>

<template>
  <div class="layout-topbar">
    <div class="layout-topbar-logo-container">
      <button
        class="layout-menu-button layout-topbar-action" @click="onWorkshopToggle"
      >
        <i class="pi pi-bars" />
      </button>
      <img :src="logo" alt="Logo" class="ml-4 h-12">
    </div>
    <div class="layout-topbar-actions">
      <div class="layout-config-menu">
        <button type="button" class="layout-topbar-action" @click="aggregationStore.toggleSearch">
          <i class="pi pi-search" />
        </button>
        <button type="button" class="layout-topbar-action" @click="toggleFullscreen">
          <i class="pi" :class="isFullscreen ? 'pi-window-minimize' : 'pi-window-maximize'" />
        </button>
        <button type="button" class="layout-topbar-action" @click="togglePage">
          <i class="pi pi-arrow-right-arrow-left" />
        </button>
      </div>

      <div class="relative">
        <button
          v-styleclass="{ selector: '@next', enterFromClass: 'hidden', enterActiveClass: 'animate-scale-in animate-duration-100', leaveToClass: 'hidden', leaveActiveClass: 'animate-fade-out animate-duration-100', hideOnOutsideClick: true }"
          type="button"
          class="layout-topbar-action layout-topbar-action-highlight"
        >
          <i class="pi pi-palette" />
        </button>
        <AppConfigurator />
      </div>
    </div>
  </div>
</template>
