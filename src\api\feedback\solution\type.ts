export interface FeedbackTriggerSolution {
  id: string // 主键ID
  solution: string // 解决方案
  solverId: string // 解决人ID
  solverName: string // 解决人名称
  solveTime: string // 解决时间
  triggerRecordId: string // 触发记录ID
  triggerSendId: string // 发送记录ID
  submitted?: boolean // 是否已提交
  submitTime?: string // 提交时间
  rejected?: boolean // 是否已退回
  rejectReason?: string // 退回原因
  rejectorId?: string // 退回人ID
  rejectorName?: string // 退回人名称
  rejectTime?: string // 退回时间
}

export interface FeedbackTriggerSolutionCreateParam {
  solution: string // 解决方案内容
  solveTime: Date // 解决时间
  triggerRecordId: string // 触发记录ID
  triggerSendId: string // 发送记录ID
  solverId?: string // 解决人ID
  solverName?: string // 解决人名称
}

export interface FeedbackTriggerUpdateParam {
  solution: string // 解决方案内容
  solveTime: Date // 解决时间
  triggerRecordId: string // 触发记录ID
  triggerSendId: string // 发送记录ID
  solverId: string // 解决人ID
  solverName: string // 解决人名称
}

export interface FeedbackTriggerSearchParam {
  action: 'view' | 'edit' | 'close' // 权限模式：view（仅查看）、edit（可编辑）、close（关闭异常）
  triggerRecordId: string // 触发记录ID
  triggerSendId: string // 发送记录ID
  userId?: string // 用户ID
  userName?: string // 用户名称
}

/**
 * 解决方案退回请求对象
 */
export interface SolutionRejectRequest {
  rejectReason: string // 退回原因
  rejectorId: string // 退回人ID
  rejectorName: string // 退回人名称
}
