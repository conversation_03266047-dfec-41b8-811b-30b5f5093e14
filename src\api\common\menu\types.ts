export interface MenuItemData extends MenuItemCreateParam {
  id: string
}

export enum TargetEnum {
  blank = '_blank',
  self = '_self',
  parent = '_parent',
}

export interface MenuItemCreateParam {
  title: string
  icon?: string
  target: TargetEnum
  access?: string
  parentId?: string
  path: string
  order: number
  url?: string
  redirect: boolean
}

export type MenuItemUpdateParam = MenuItemCreateParam

export interface MenuSearchParam {
  path?: string
  title?: string
}
