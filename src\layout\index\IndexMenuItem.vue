<script setup lang="ts">
import { onBeforeMount, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import type { Workshop } from './types'

const props = defineProps<{
  item: Workshop
  index: number
}>()

const router = useRouter()
const { setActiveWorkShopItem, onWorkshopToggle, workshopState } = useWorkshop()

const isActiveWorkShop = ref(false)
const itemKey = ref<string | null>(null)

onBeforeMount(() => {
  itemKey.value = props.item.code

  const activeWorkShopItemKey = workshopState.activeWorkShopItemKey

  isActiveWorkShop.value = activeWorkShopItemKey === itemKey.value
})

watch(
  () => workshopState.activeWorkShopItemKey,
  (newVal) => {
    isActiveWorkShop.value = newVal === itemKey.value || false
  },
)

function itemClick() {
  // 切换产品线
  if (workshopState.staticWorkShopMobileActive || workshopState.overlayWorkShopActive) {
    onWorkshopToggle()
  }

  // 设置产品线
  const foundItemKey = itemKey.value
  setActiveWorkShopItem(foundItemKey)

  // 跳转到对应的 index
  if (props.item.code) {
    router.push(`/${props.item.code}`)
  }
}

function checkActiveRoute(item: Workshop) {
  return workshopState.activeWorkShopItemKey === item.code
}
</script>

<template>
  <li :class="{ 'active-menuitem': isActiveWorkShop }">
    <a v-if="item.code " :class="{ 'active-route': checkActiveRoute(item) }" tabindex="0" @click="itemClick()">
      <span class="layout-menuitem-text">{{ item.name }}</span>
    </a>
  </li>
</template>
