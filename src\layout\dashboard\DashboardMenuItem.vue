<script setup lang="ts">
import { onBeforeMount, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import type { Productline } from './productline'

const props = defineProps<{
  item: Productline
  index: number
}>()

const router = useRouter()
const { setActiveLineItem, onProductlineToggle, lineState } = useProductline()

const isActiveLine = ref(false)
const itemKey = ref<string | null>(null)

onBeforeMount(() => {
  itemKey.value = props.item.code

  const activeLineItemKey = lineState.activeLineItemKey

  isActiveLine.value = activeLineItemKey === itemKey.value
})

watch(
  () => lineState.activeLineItemKey,
  (newVal) => {
    isActiveLine.value = newVal === itemKey.value || false
  },
)

function itemClick() {
  // 切换产品线
  if (lineState.staticLineMobileActive || lineState.overlayLineActive) {
    onProductlineToggle()
  }

  // 设置产品线
  const foundItemKey = itemKey.value
  setActiveLineItem(foundItemKey)

  // 跳转到对应的 dashboard
  if (props.item.code) {
    router.push(`/dashboard/${props.item.code}`)
  }
}

function checkActiveRoute(item: Productline) {
  return lineState.activeLineItemKey === item.code
}
</script>

<template>
  <li :class="{ 'active-menuitem': isActiveLine }">
    <a v-if="item.code " :class="{ 'active-route': checkActiveRoute(item) }" tabindex="0" @click="itemClick()">
      <span class="layout-menuitem-text">{{ item.name }}</span>
    </a>
  </li>
</template>
