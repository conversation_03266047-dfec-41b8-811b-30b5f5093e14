import type {
  FeedbackTriggerSend,
  FeedbackTriggerSendCreateParam,
  FeedbackTriggerSendSearchParam,
  FeedbackTriggerSendUpdateParam,
  FeedbackTriggerSendWithId,
} from './type'
import type { PageList, Pageable } from '~/api/common/type'
import { kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const TriggerSendApi = {
  /**
   * 创建快返触发发送记录
   */
  create: (param: FeedbackTriggerSendCreateParam) =>
    kyPost('feedback-trigger-send', param).json<FeedbackTriggerSendWithId>(),

  /**
   * 分页查询快返触发发送记录
   */
  page: (param: Pageable<FeedbackTriggerSendSearchParam>) =>
    kyPost('feedback-trigger-send/page', param).json<PageList<FeedbackTriggerSendWithId>>(),

  /**
   * 根据ID获取快返触发发送记录
   */
  findById: (id: string) =>
    kyGet(`feedback-trigger-send/${id}`).json<FeedbackTriggerSend>(),

  /**
   * 更新快返触发发送记录
   */
  update: (id: string, param: FeedbackTriggerSendUpdateParam) =>
    kyPut(`feedback-trigger-send/${id}`, param).json<FeedbackTriggerSendWithId>(),

  /**
   * 删除快返触发发送记录
   */
  delete: (id: string) =>
    kyDelete(`feedback-trigger-send/${id}`),

  /**
   * 批量删除快返触发发送记录
   */
  batchDelete: (ids: string[]) =>
    kyPost('feedback-trigger-send/batch-delete', { ids }),

  /**
   * 根据触发记录ID获取发送记录列表
   */
  findByTriggerRecordId: (triggerRecordId: string) =>
    kyGet(`feedback-trigger-send/by-trigger-record/${triggerRecordId}`).json<FeedbackTriggerSendWithId[]>(),

  /**
   * 手动发送
   */
  send: (id: string) =>
    kyPost(`feedback-trigger-send/${id}/perform-send`).text(),

  /**
   * 批量发送
   */
  batchSend: (ids: string[]) =>
    kyPost('feedback-trigger-send/batch-send', { ids }),
}
