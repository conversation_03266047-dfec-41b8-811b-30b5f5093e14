<script setup lang="ts">
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  options: any[]
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <Select v-model="value" :input-id="props.name" :options="options" option-label="label" option-value="value" :invalid="errorMessage ? true : false" fluid v-bind="$attrs" />
  <ErrorMsg :error-message="errorMessage" />
</template>
