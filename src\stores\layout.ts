import { $t, updatePreset, updateSurfacePalette } from '@primeuix/themes'
import { defineStore } from 'pinia'
import { computed } from 'vue'
import { type MenuMode, type Palette, type PresetType, presets, primaryColors, surfaces } from '~/config/layout'

const dark = useDark()
interface LayoutConfig {
  preset: PresetType
  primary: string
  surface: string | null
  menuMode: MenuMode
}

export const useLayoutStore = defineStore('layout', () => {
  const layoutConfig = ref<LayoutConfig>({
    preset: 'Aura',
    primary: 'emerald',
    surface: null,
    menuMode: 'overlay',
  })

  const executeDarkModeToggle = () => {
    dark.value = !dark.value
  }
  const toggleDarkMode = () => {
    if (!document.startViewTransition) {
      executeDarkModeToggle()
      return
    }

    document.startViewTransition(() => executeDarkModeToggle())
  }

  const getPrimary = computed(() => layoutConfig.value.primary)

  const getSurface = computed(() => layoutConfig.value.surface)

  function getPresetExt() {
    const color = primaryColors.find(c => c.name === layoutConfig.value.primary)
    if (!color || color.name === 'noir') {
      return {
        semantic: {
          primary: {
            50: '{surface.50}',
            100: '{surface.100}',
            200: '{surface.200}',
            300: '{surface.300}',
            400: '{surface.400}',
            500: '{surface.500}',
            600: '{surface.600}',
            700: '{surface.700}',
            800: '{surface.800}',
            900: '{surface.900}',
            950: '{surface.950}',
          },
          colorScheme: {
            light: {
              primary: {
                color: '{primary.950}',
                contrastColor: '#ffffff',
                hoverColor: '{primary.800}',
                activeColor: '{primary.700}',
              },
              highlight: {
                background: '{primary.950}',
                focusBackground: '{primary.700}',
                color: '#ffffff',
                focusColor: '#ffffff',
              },
            },
            dark: {
              primary: {
                color: '{primary.50}',
                contrastColor: '{primary.950}',
                hoverColor: '{primary.200}',
                activeColor: '{primary.300}',
              },
              highlight: {
                background: '{primary.50}',
                focusBackground: '{primary.300}',
                color: '{primary.950}',
                focusColor: '{primary.950}',
              },
            },
          },
        },
      }
    }
    else {
      return {
        semantic: {
          primary: color.palette,
          colorScheme: {
            light: {
              primary: {
                color: '{primary.500}',
                contrastColor: '#ffffff',
                hoverColor: '{primary.600}',
                activeColor: '{primary.700}',
              },
              highlight: {
                background: '{primary.50}',
                focusBackground: '{primary.100}',
                color: '{primary.700}',
                focusColor: '{primary.800}',
              },
            },
            dark: {
              primary: {
                color: '{primary.400}',
                contrastColor: '{surface.900}',
                hoverColor: '{primary.300}',
                activeColor: '{primary.200}',
              },
              highlight: {
                background: 'color-mix(in srgb, {primary.400}, transparent 84%)',
                focusBackground: 'color-mix(in srgb, {primary.400}, transparent 76%)',
                color: 'rgba(255,255,255,.87)',
                focusColor: 'rgba(255,255,255,.87)',
              },
            },
          },
        },
      }
    }
  }

  function updatePrimary(color: Palette) {
    layoutConfig.value.primary = color.name
    updatePreset(getPresetExt())
  }

  function updateSurface(color: Palette) {
    layoutConfig.value.surface = color.name
    updateSurfacePalette(color.palette)
  }

  function changePreset(preset: PresetType) {
    layoutConfig.value.preset = preset
    const presetValue = presets[layoutConfig.value.preset]
    const surfacePalette = surfaces.find(s => s.name === layoutConfig.value.surface)?.palette

    $t().preset(presetValue).preset(getPresetExt()).surfacePalette(surfacePalette).use({ useDefaultOptions: true })
  }

  function updateMenuMode(menuMode: MenuMode) {
    layoutConfig.value.menuMode = menuMode
  }

  function init() {
    const presetValue = presets[layoutConfig.value.preset]
    const surfacePalette = surfaces.find(s => s.name === layoutConfig.value.surface)?.palette
    $t().preset(presetValue).preset(getPresetExt()).surfacePalette(surfacePalette).use({ useDefaultOptions: true })
  }

  return { layoutConfig, getPrimary, getSurface, toggleDarkMode, updatePreset, updatePrimary, updateSurface, changePreset, updateMenuMode, init }
}, {
  persistedState: {
    persist: true,
  },
})
