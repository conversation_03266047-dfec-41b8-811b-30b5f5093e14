export interface YamahaSmtLog {
  id: string
  filename: string
  logtime: Date
  language: string
  substratename: string
  substrateremark: string
  productionstarttime: Date
  productionendtime: Date
  plannedsheets: number
  batchsequencenumber: number
  endflag: number
  mountingcta: number
  mountingctb: number
  mountingctc: number
  mountingctd: number
  transferct: number
  standbyct: number
  markrecognitioncta: number
  markrecognitionctb: number
  markrecognitionctc: number
  markrecognitionctd: number
  suctionerrorcount: number
  componentrecognitionerror: number
  liftingfooterrorcount: number
  markrecognitionerrorcount: number
  transfererrorcount: number
  othererrorcount: number
  errorshutdowncount: number
  errorshutdowntime: number
  errorrecoverytime: number
  badboardngcount: number
  completedpanelcount: number
  conveyingtable: number
  upstreamstandbytime: number
  downstreamstandbytime: number
  operatorshutdowntime: number
  track: number
  awaitingothertracktime: number
  substrateid: string
  placeholder1: string
  transferstopoffset: number
  transferteachingflag: number
  productionvariety: string
  substratesurfaceinfo: string
  productionbatch: string
  placeholder2: string
  otherconveyingtabletime: number
  placeholder3: string
  placeholder4: string
  placeholder5: string
  placeholder6: string
  placeholder7: string
  placeholder8: string
}
