import type { AlarmInfo, AnalyzeQueryParam, AnalyzeResult, DefectTypeResult, DeviceCtInfo, HourlyOutputList, LineChangeoverInfo, ProductionLineResult } from './type'
import { kyPost } from '~/utils/request'

export const analyzeApi = {
  // 获取当前产品
  getCurrentProduct: (param: AnalyzeQueryParam) => kyPost('analyze/current-product', param).json<ProductionLineResult>(),
  // 获取OEE等指标
  getLineOee: (param: AnalyzeQueryParam) => kyPost('analyze/line-oee', param).json<AnalyzeResult>(),
  // 获取产线换模次数
  getLineChangeovers: (param: AnalyzeQueryParam) => kyPost('analyze/line-changeovers', param).json<LineChangeoverInfo>(),
  // 获取设备CT
  getDevicesCt: (param: AnalyzeQueryParam) => kyPost('analyze/devices-ct', param).json<DeviceCtInfo[]>(),
  // 获取小时产量
  getHourlyOutput: (param: AnalyzeQueryParam) => kyPost('analyze/hourly-output', param).json<HourlyOutputList>(),
  // 获取缺陷类型
  getDefectTypes: (param: AnalyzeQueryParam) => kyPost('analyze/defect-types', param).json<DefectTypeResult>(),
  // 获取TOP5（异常报警）
  getTopAlarms: (param: AnalyzeQueryParam) => kyPost('analyze/top-alarms', param).json<AlarmInfo[]>(),
  // 获取服务器时间
  getServerTime: () => kyPost('analyze/server-time').json<number>(),
  // 导出车间日报Excel
  exportDailyExcel: (workShopId: string, param: AnalyzeQueryParam) => kyPost(`analyze/export/daily/${workShopId}`, param),
}
