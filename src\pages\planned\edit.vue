<script setup lang="ts">
import { usePlannedCapacityEditForm } from './schema'
import { capacityApi } from '~/api/capacity'
import type { PlannedCapacity } from '~/api/capacity/type'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const { resetForm, handleSubmit, setValues } = usePlannedCapacityEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await capacityApi.update(props.id, values)
      success('创建成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const plannedCapacity: PlannedCapacity = await capacityApi.get(props.id)
    setValues(plannedCapacity)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="编辑计划产能" @show="loadData">
    <form @submit="save">
      <FormLayout>
        <LDictSelect name="lineCode" label="线体编码" code="LINE_CODE" />
        <LInput name="productModel" label="产品型号" />
        <LInputNumber name="plannedQuantity" label="标准产能" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
